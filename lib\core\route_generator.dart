import 'package:flutter/material.dart';
import '../presentation/views/Auth/login_view.dart';
import '../presentation/views/home/<USER>';
import '../presentation/views/pets/no_pets_view.dart';
import '../presentation/controllers/theme_controller.dart';

class RouteGenerator {
  static Route<dynamic> generateRoute(RouteSettings settings, {required ThemeController themeController}) {
    switch (settings.name) {
      case '/':
        return MaterialPageRoute(
          builder: (_) => LoginView(themeController: themeController),
        );
      case '/home':
        return MaterialPageRoute(
          builder: (_) => HomeView(themeController: themeController),
        );
      case '/pets':
        return MaterialPageRoute(
          builder: (_) => NoPetsView(themeController: themeController),
        );
      // case '/add-pet':
      //   return MaterialPageRoute(
      //     builder: (_) => AddPetView(themeController: themeController),
      //   );
      default:
        return MaterialPageRoute(
          builder: (_) => Scaffold(
            body: Center(child: Text('No route defined for  ${settings.name}')),
          ),
        );
    }
  }
} 