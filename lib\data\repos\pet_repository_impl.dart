import '../../domain/entities/create_pet_request.dart';
import '../../domain/entities/create_pet_response.dart';
import '../../domain/entities/update_pet_request.dart';
import '../../domain/entities/update_pet_response.dart';
import '../../domain/entities/pet_response.dart';
import '../../domain/repositories/pet_repository.dart';
import '../sources/remote/pet_remote_data_source.dart';

class PetRepositoryImpl implements PetRepository {
  final PetRemoteDataSource remoteDataSource;

  PetRepositoryImpl({
    required this.remoteDataSource,
  });

  @override
  Future<CreatePetResponse> createPet(CreatePetRequest request) async {
    return await remoteDataSource.createPet(request);
  }

  @override
  Future<UpdatePetResponse> updatePet(UpdatePetRequest request) async {
    return await remoteDataSource.updatePet(request);
  }

  @override
  Future<List<PetResponse>> getAllPets() async {
    return await remoteDataSource.getAllPets();
  }

  @override
  Future<bool> deletePet(String petId) async {
    try {
      return await remoteDataSource.deletePet(petId);
    } catch (e) {
      print('❌ Repository error deleting pet: $e');
      rethrow;
    }
  }
}
