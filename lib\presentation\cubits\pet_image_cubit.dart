import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';

// States
abstract class PetImageState extends Equatable {
  @override
  List<Object?> get props => [];
}

class PetImageInitial extends PetImageState {}

class PetImageLoading extends Pet<PERSON><PERSON>State {}

class PetImageLoaded extends PetImageState {
  final String imageUrl;

  PetImageLoaded(this.imageUrl);

  @override
  List<Object?> get props => [imageUrl];
}

class PetImageError extends PetImageState {
  final String message;

  PetImageError(this.message);

  @override
  List<Object?> get props => [message];
}

class PetImageEmpty extends PetImageState {}

// Cubit
class PetImageCubit extends Cubit<PetImageState> {
  static const String baseImageUrl = 'https://squeakapi.veticareapp.com:8001/images';

  PetImageCubit() : super(PetImageInitial());

  void loadImage(String? imageName) {
    if (imageName == null || imageName.isEmpty) {
      emit(PetImageEmpty());
      return;
    }

    emit(PetImageLoading());
    
    try {
      final imageUrl = '$baseImageUrl/$imageName';
      emit(PetImageLoaded(imageUrl));
    } catch (e) {
      emit(PetImageError('Failed to load image: ${e.toString()}'));
    }
  }

  void onImageError() {
    emit(PetImageError('Failed to load image from server'));
  }

  void reset() {
    emit(PetImageInitial());
  }
}
