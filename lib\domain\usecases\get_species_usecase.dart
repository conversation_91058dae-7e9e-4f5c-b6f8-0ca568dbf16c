import '../entities/species.dart';
import '../repositories/species_repository.dart';

class GetSpeciesUseCase {
  final SpeciesRepository repository;

  GetSpeciesUseCase(this.repository);

  Future<List<Species>> call() async {
    try {
      // First check if we have cached data
      if (await repository.hasCachedSpecies()) {
        print('🔄 Loading species from cache');
        return await repository.getCachedSpecies();
      }

      // If no cache, fetch from API
      print('🌐 Fetching species from API');
      final species = await repository.getSpecies();
      
      // Cache the results
      await repository.cacheSpecies(species);
      print('💾 Species cached successfully');
      
      return species;
    } catch (e) {
      print('❌ Error in GetSpeciesUseCase: $e');
      
      // Try to return cached data as fallback
      if (await repository.hasCachedSpecies()) {
        print('🔄 Falling back to cached species data');
        return await repository.getCachedSpecies();
      }
      
      rethrow;
    }
  }
}
