import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:shared_preferences/shared_preferences.dart';

// States
abstract class ThemeState extends Equatable {
  const ThemeState();

  @override
  List<Object> get props => [];
}

class ThemeInitial extends ThemeState {}

class ThemeLoaded extends ThemeState {
  final bool isDark;

  const ThemeLoaded(this.isDark);

  @override
  List<Object> get props => [isDark];
}

// Cubit
class ThemeCubit extends Cubit<ThemeState> {
  ThemeCubit() : super(ThemeInitial()) {
    _loadTheme();
  }

  Future<void> _loadTheme() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final isDark = prefs.getBool('isDarkTheme') ?? true;
      emit(ThemeLoaded(isDark));
    } catch (e) {
      emit(const ThemeLoaded(true)); // Default to dark theme
    }
  }

  Future<void> toggleTheme() async {
    if (state is ThemeLoaded) {
      final currentState = state as ThemeLoaded;
      final newIsDark = !currentState.isDark;
      
      try {
        final prefs = await SharedPreferences.getInstance();
        await prefs.setBool('isDarkTheme', newIsDark);
        emit(ThemeLoaded(newIsDark));
      } catch (e) {
        // If saving fails, still emit the new state
        emit(ThemeLoaded(newIsDark));
      }
    }
  }

  bool get isDark {
    if (state is ThemeLoaded) {
      return (state as ThemeLoaded).isDark;
    }
    return true; // Default to dark
  }
}
