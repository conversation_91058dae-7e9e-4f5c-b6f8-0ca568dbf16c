import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import '../../core/di/dependency_injection.dart';
import '../../services/pet_service.dart';
import '../../domain/entities/pet_response.dart';
import '../controllers/home/<USER>';
import 'pet_cache_cubit.dart';
import 'pet_list_cubit.dart';

// States
abstract class PetActionsState extends Equatable {
  const PetActionsState();

  @override
  List<Object?> get props => [];
}

class PetActionsInitial extends PetActionsState {}

class PetActionsLoading extends PetActionsState {
  final String petId;
  final String actionType; // 'delete' or 'edit'

  const PetActionsLoading({
    required this.petId,
    required this.actionType,
  });

  @override
  List<Object?> get props => [petId, actionType];
}

class PetActionsSuccess extends PetActionsState {
  final String message;
  final String actionType;
  final String petId;

  const PetActionsSuccess({
    required this.message,
    required this.actionType,
    required this.petId,
  });

  @override
  List<Object?> get props => [message, actionType, petId];
}

class PetActionsError extends PetActionsState {
  final String message;
  final String actionType;
  final String petId;

  const PetActionsError({
    required this.message,
    required this.actionType,
    required this.petId,
  });

  @override
  List<Object?> get props => [message, actionType, petId];
}

class PetActionsCubit extends Cubit<PetActionsState> {
  final PetService _petService;
  final NavigationController? _navigationController;
  final PetCacheCubit? _petCacheCubit;
  final PetListCubit? _petListCubit;

  PetActionsCubit({
    PetService? petService,
    NavigationController? navigationController,
    PetCacheCubit? petCacheCubit,
    PetListCubit? petListCubit,
  })  : _petService = petService ?? getIt<PetService>(),
        _navigationController = navigationController,
        _petCacheCubit = petCacheCubit,
        _petListCubit = petListCubit,
        super(PetActionsInitial());

  Future<void> deletePet(PetResponse pet) async {
    emit(PetActionsLoading(petId: pet.id, actionType: 'delete'));

    try {
      print('🗑️ Starting pet deletion for: ${pet.petName} (ID: ${pet.id})');
      
      // Perform the deletion
      final success = await _petService.deletePet(pet.id);
      
      if (success) {
        print('✅ Pet deleted successfully: ${pet.petName}');
        
        // Clear cache to force fresh data
        await _petCacheCubit?.clearCache();
        
        // Reload pets list with fresh data
        await _petListCubit?.loadPets(forceRefresh: true);
        
        emit(PetActionsSuccess(
          message: 'Pet "${pet.petName}" deleted successfully!',
          actionType: 'delete',
          petId: pet.id,
        ));
      } else {
        print('❌ Pet deletion failed: ${pet.petName}');
        emit(PetActionsError(
          message: 'Failed to delete pet "${pet.petName}"',
          actionType: 'delete',
          petId: pet.id,
        ));
      }
    } catch (e) {
      final errorMessage = 'Failed to delete pet "${pet.petName}": ${e.toString()}';
      print('❌ Pet deletion error: $errorMessage');
      emit(PetActionsError(
        message: errorMessage,
        actionType: 'delete',
        petId: pet.id,
      ));
    }
  }

  void navigateToEditPet(PetResponse pet) {
    try {
      print('✏️ Navigating to edit pet: ${pet.petName} (ID: ${pet.id})');
      
      emit(PetActionsLoading(petId: pet.id, actionType: 'edit'));
      
      // Navigate to pets tab first (in case we're not already there)
      _navigationController?.setTab(NavigationTab.pets);
      
      emit(PetActionsSuccess(
        message: 'Opening edit form for "${pet.petName}"',
        actionType: 'edit',
        petId: pet.id,
      ));
    } catch (e) {
      final errorMessage = 'Failed to navigate to edit pet: ${e.toString()}';
      print('❌ Navigation error: $errorMessage');
      emit(PetActionsError(
        message: errorMessage,
        actionType: 'edit',
        petId: pet.id,
      ));
    }
  }

  void reset() {
    emit(PetActionsInitial());
  }
}
