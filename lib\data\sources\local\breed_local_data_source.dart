import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../domain/entities/breed.dart';

abstract class BreedLocalDataSource {
  Future<List<Breed>> getCachedBreeds();
  Future<void> cacheBreeds(List<Breed> breeds);
  Future<bool> hasCachedBreeds();
  Future<void> clearCache();
}

class BreedLocalDataSourceImpl implements BreedLocalDataSource {
  static const String _breedsKey = 'cached_breeds';
  static const String _cacheTimestampKey = 'breeds_cache_timestamp';
  static const int _cacheValidityHours = 24; // Cache valid for 24 hours

  @override
  Future<List<Breed>> getCachedBreeds() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final breedsJson = prefs.getString(_breedsKey);
      
      if (breedsJson == null) {
        print('📱 No cached breeds found');
        return [];
      }

      // Check if cache is still valid
      final cacheTimestamp = prefs.getInt(_cacheTimestampKey) ?? 0;
      final now = DateTime.now().millisecondsSinceEpoch;
      final cacheAge = Duration(milliseconds: now - cacheTimestamp);
      
      if (cacheAge.inHours > _cacheValidityHours) {
        print('⏰ Cache expired (${cacheAge.inHours} hours old), clearing...');
        await clearCache();
        return [];
      }

      final List<dynamic> jsonList = jsonDecode(breedsJson);
      final breeds = jsonList.map((json) => Breed.fromJson(json as Map<String, dynamic>)).toList();
      
      print('📱 Loaded ${breeds.length} breeds from cache (${cacheAge.inHours}h old)');
      return breeds;
    } catch (e) {
      print('❌ Error loading cached breeds: $e');
      await clearCache(); // Clear corrupted cache
      return [];
    }
  }

  @override
  Future<void> cacheBreeds(List<Breed> breeds) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final breedsJson = jsonEncode(breeds.map((breed) => breed.toJson()).toList());
      
      await prefs.setString(_breedsKey, breedsJson);
      await prefs.setInt(_cacheTimestampKey, DateTime.now().millisecondsSinceEpoch);
      
      print('💾 Cached ${breeds.length} breeds successfully');
    } catch (e) {
      print('❌ Error caching breeds: $e');
      rethrow;
    }
  }

  @override
  Future<bool> hasCachedBreeds() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final breedsJson = prefs.getString(_breedsKey);
      
      if (breedsJson == null) return false;

      // Check if cache is still valid
      final cacheTimestamp = prefs.getInt(_cacheTimestampKey) ?? 0;
      final now = DateTime.now().millisecondsSinceEpoch;
      final cacheAge = Duration(milliseconds: now - cacheTimestamp);
      
      return cacheAge.inHours <= _cacheValidityHours;
    } catch (e) {
      print('❌ Error checking cached breeds: $e');
      return false;
    }
  }

  @override
  Future<void> clearCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_breedsKey);
      await prefs.remove(_cacheTimestampKey);
      print('🗑️ Breed cache cleared');
    } catch (e) {
      print('❌ Error clearing breed cache: $e');
    }
  }
}
