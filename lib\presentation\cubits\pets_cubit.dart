import 'package:flutter_bloc/flutter_bloc.dart';
import '../../domain/entities/pet.dart';

abstract class PetsState {}

class PetsInitial extends PetsState {}

class PetsLoading extends PetsState {}

class PetsLoaded extends PetsState {
  final List<Pet> pets;
  PetsLoaded(this.pets);
}

class Pets<PERSON>rror extends PetsState {
  final String message;
  PetsError(this.message);
}

// Cubit
class PetsCubit extends Cubit<PetsState> {
  final List<Pet> _pets = [];

  PetsCubit() : super(PetsInitial());

  Future<void> loadPets() async {
    emit(PetsLoading());
    try {
      emit(PetsLoaded(_pets));
    } catch (e) {
      emit(PetsError(e.toString()));
    }
  }

  Future<void> addPet(Pet pet) async {
    final currentState = state;
    if (currentState is PetsLoaded) {
      _pets.add(pet);
      emit(PetsLoaded(_pets));
    }
  }

  bool get hasPets {
    final state = this.state;
    if (state is PetsLoaded) {
      return state.pets.isNotEmpty;
    }
    return false;
  }
}
