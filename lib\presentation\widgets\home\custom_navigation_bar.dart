import 'package:flutter/material.dart';
import '../../controllers/home/<USER>';

class CustomNavigationBar extends StatelessWidget {
  final NavigationController controller;
  
  const CustomNavigationBar({
    Key? key,
    required this.controller,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ListenableBuilder(
      listenable: controller,
      builder: (context, _) {
        return Container(
          height: 80,
          decoration: BoxDecoration(
            color: Theme.of(context).scaffoldBackgroundColor,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 10,
                offset: const Offset(0, -2),
              ),
            ],
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildNavItem(NavigationTab.settings, Icons.settings),
              _buildNavItem(NavigationTab.history, Icons.access_time),
              _buildNavItem(NavigationTab.pets, Icons.pets),
              _buildNavItem(NavigationTab.addUser, Icons.person_add),
              _buildNavItem(NavigationTab.home, Icons.home),
            ],
          ),
        );
      },
    );
  }

  Widget _buildNavItem(NavigationTab tab, IconData icon) {
    final isSelected = controller.isSelected(tab);
    
    return GestureDetector(
      onTap: () => controller.setTab(tab),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected ? const Color(0xFF6D5DF6) : Colors.transparent,
          borderRadius: BorderRadius.circular(20),
          boxShadow: isSelected ? [
            BoxShadow(
              color: const Color(0xFF6D5DF6).withValues(alpha: 0.3),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ] : null,
        ),
        child: AnimatedScale(
          duration: const Duration(milliseconds: 200),
          scale: isSelected ? 1.2 : 1.0,
          child: Icon(
            icon,
            color: isSelected ? Colors.white : Colors.grey[600],
            size: 28,
          ),
        ),
      ),
    );
  }
} 