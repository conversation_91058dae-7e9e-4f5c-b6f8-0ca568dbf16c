class CreatePetResponse {
  final bool success;
  final String? message;
  final Map<String, dynamic>? data;
  final Map<String, dynamic>? errors;

  const CreatePetResponse({
    required this.success,
    this.message,
    this.data,
    this.errors,
  });

  factory CreatePetResponse.fromJson(Map<String, dynamic> json) {
    return CreatePetResponse(
      success: json['success'] ?? false,
      message: json['message'],
      data: json['data'],
      errors: json['errors'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      if (message != null) 'message': message,
      if (data != null) 'data': data,
      if (errors != null) 'errors': errors,
    };
  }

  @override
  String toString() => 'CreatePetResponse(success: $success, message: $message)';
}
