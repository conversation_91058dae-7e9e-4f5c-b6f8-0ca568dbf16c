class Species {
  final String id;
  final String enName;
  final String arName;

  const Species({
    required this.id,
    required this.enName,
    required this.arName,
  });

  factory Species.fromJson(Map<String, dynamic> json) {
    return Species(
      id: json['id'] ?? '',
      enName: json['enName'] ?? '',
      arName: json['arName'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'enName': enName,
      'arName': arName,
    };
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Species &&
        other.id == id &&
        other.enName == enName &&
        other.arName == arName;
  }

  @override
  int get hashCode => id.hashCode ^ enName.hashCode ^ arName.hashCode;

  @override
  String toString() => 'Species(id: $id, enName: $enName, arName: $arName)';
}
