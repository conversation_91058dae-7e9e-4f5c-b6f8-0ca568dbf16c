import 'dart:convert';
import 'package:http/http.dart' as http;
import '../../data/models/Auth/login_response_model.dart';

class AuthService {
  Future<LoginResponseModel> login(String emailOrPhoneNumber, String password) async {
    final url = Uri.parse('https://squeakapi.veticareapp.com:8001/v1/api/signin');
    final response = await http.post(
      url,
      headers: {'Content-Type': 'application/json'},
      body: jsonEncode({
        'emailOrPhoneNumber': emailOrPhoneNumber,
        'password': password,
        'fbToken': 'static_fb_token',
        'iosDevice': true,
        'androidevice': true,
      }),
    );

    if (response.statusCode == 200) {
      final Map<String, dynamic> json = jsonDecode(response.body);
      return LoginResponseModel.fromJson(json);
    } else {
      throw Exception('Failed to login: \\${response.statusCode}');
    }
  }

  Future<void> resetPassword(String email) async {
    await Future.delayed(const Duration(seconds: 1));
  }
}
