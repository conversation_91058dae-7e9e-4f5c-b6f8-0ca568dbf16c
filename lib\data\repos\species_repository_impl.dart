import '../../domain/entities/species.dart';
import '../../domain/repositories/species_repository.dart';
import '../sources/local/species_local_data_source.dart';
import '../sources/remote/species_remote_data_source.dart';

class SpeciesRepositoryImpl implements SpeciesRepository {
  final SpeciesRemoteDataSource remoteDataSource;
  final SpeciesLocalDataSource localDataSource;

  SpeciesRepositoryImpl({
    required this.remoteDataSource,
    required this.localDataSource,
  });

  @override
  Future<List<Species>> getSpecies() async {
    return await remoteDataSource.getSpecies();
  }

  @override
  Future<void> cacheSpecies(List<Species> species) async {
    await localDataSource.cacheSpecies(species);
  }

  @override
  Future<List<Species>> getCachedSpecies() async {
    return await localDataSource.getCachedSpecies();
  }

  @override
  Future<bool> hasCachedSpecies() async {
    return await localDataSource.hasCachedSpecies();
  }
}
