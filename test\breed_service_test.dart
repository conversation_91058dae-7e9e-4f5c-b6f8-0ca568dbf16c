import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:first_app/core/di/dependency_injection.dart';
import 'package:first_app/services/breed_service.dart';
import 'package:first_app/domain/entities/pet_data.dart';

void main() {
  group('BreedService Tests', () {
    setUp(() async {
      // Initialize DI and Mock SharedPreferences
      await DependencyInjection.init();
      SharedPreferences.setMockInitialValues({
        'token': 'mock_token_for_testing',
      });
    });

    test('should load breeds and update PetData', () async {
      // Arrange
      final breedService = getIt<BreedService>();
      final initialSpeciesCount = PetData.species.length;

      // Act
      await breedService.loadAndUpdateBreeds();

      // Assert
      expect(PetData.species.length, greaterThanOrEqualTo(initialSpeciesCount));
      
      // Check if we have the expected species
      final speciesNames = PetData.species.map((s) => s.name).toList();
      expect(speciesNames, contains('Dog'));
      expect(speciesNames, contains('Cat'));
      
      // Check if breeds are populated
      for (final species in PetData.species) {
        expect(species.breeds, isNotEmpty);
        expect(species.breeds, contains('Other')); // Should always have 'Other' option
      }
    });

    test('should handle cached breeds', () async {
      // Arrange
      final breedService = getIt<BreedService>();

      // Act - First call should fetch from API and cache
      await breedService.loadAndUpdateBreeds();
      final firstCallSpecies = List.from(PetData.species);

      // Act - Second call should use cache
      await breedService.loadAndUpdateBreeds();
      final secondCallSpecies = List.from(PetData.species);

      // Assert - Both calls should return the same data
      expect(firstCallSpecies.length, equals(secondCallSpecies.length));
    });
  });
}
