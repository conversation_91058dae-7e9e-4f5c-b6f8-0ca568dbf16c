import 'package:flutter/material.dart';
import '../../controllers/home/<USER>';

class HomeContentView extends StatelessWidget {
  final bool isDark;
  final HomeController homeController;

  const HomeContentView({
    Key? key, 
    required this.isDark,
    required this.homeController,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<String?>(
      future: _getFullName(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }
        final fullName = snapshot.data ?? '';
        return Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: isDark
                  ? [const Color(0xFF232526), const Color(0xFF414345)]
                  : [
                      const Color(0xFF6D5DF6).withValues(alpha: 0.1),
                      const Color(0xFF46A0FC).withValues(alpha: 0.1),
                    ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
          child: Center(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                CircleAvatar(
                  radius: 56,
                  backgroundImage: const AssetImage('assets/sign_up.png'),
                  backgroundColor: Colors.transparent,
                ),
                const SizedBox(height: 24),
                const Text(
                  'Welcome',
                  style: TextStyle(
                    fontSize: 28,
                    fontWeight: FontWeight.w900,
                    letterSpacing: 1.1,
                    color: Color(0xFF6D5DF6),
                    shadows: [
                      Shadow(
                        blurRadius: 6,
                        color: Colors.black12,
                        offset: Offset(0, 2),
                      ),
                    ],
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                Text(
                  fullName,
                  style: TextStyle(
                    fontSize: 22,
                    fontWeight: FontWeight.bold,
                    color: isDark ? Colors.amber : const Color(0xFF333366),
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 12),
                FutureBuilder<String?>(
                  future: _getPhone(),
                  builder: (context, phoneSnapshot) {
                    if (phoneSnapshot.connectionState ==
                        ConnectionState.waiting) {
                      return const SizedBox.shrink();
                    }
                    final phone = phoneSnapshot.data ?? '';
                    if (phone.isEmpty) return const SizedBox.shrink();
                    return Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const Icon(Icons.phone, size: 20, color: Colors.grey),
                        const SizedBox(width: 8),
                        Text(
                          phone,
                          style: TextStyle(
                            fontSize: 18,
                            color: isDark ? Colors.white : Colors.black87,
                          ),
                        ),
                      ],
                    );
                  },
                ),
                const SizedBox(height: 32),
              ],
            ),
          ),
        );
      },
    );
  }

  Future<String?> _getFullName() async {
    return homeController.getFullName();
  }

  Future<String?> _getPhone() async {
    return homeController.getPhone();
  }
}
