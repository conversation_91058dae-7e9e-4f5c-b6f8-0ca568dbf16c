class UpdatePetRequest {
  final String id;
  final String petName;
  final int gender;
  final String breedId;
  final String specieId;
  final DateTime birthdate;
  final bool isSpayed;
  final String? imageName;
  final String? passportImage;
  final String? passportnumber;
  final String? microShipNumber;

  const UpdatePetRequest({
    required this.id,
    required this.petName,
    required this.gender,
    required this.breedId,
    required this.specieId,
    required this.birthdate,
    required this.isSpayed,
    this.imageName,
    this.passportImage,
    this.passportnumber,
    this.microShipNumber,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'petName': petName,
      'gender': gender,
      'breedId': breedId,
      'specieId': specieId,
      'birthdate': birthdate.toIso8601String(),
      'isSpayed': isSpayed,
      if (imageName != null) 'imageName': imageName,
      if (passportImage != null) 'passportImage': passportImage,
      if (passportnumber != null) 'passportnumber': passportnumber,
      if (microShipNumber != null) 'microShipNumber': microShipNumber,
    };
  }

  @override
  String toString() => 'UpdatePetRequest(id: $id, petName: $petName)';
}
