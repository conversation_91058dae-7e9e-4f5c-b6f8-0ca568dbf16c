import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:image_picker/image_picker.dart';
import '../../controllers/theme_controller.dart';
import '../../controllers/home/<USER>';
import '../../cubits/add_pet_cubit.dart';
import '../../cubits/edit_pet_cubit.dart';
import '../../cubits/theme_cubit.dart';
import '../../cubits/pet_operation_cubit.dart';
import '../../cubits/pet_cache_cubit.dart';
import '../../cubits/pet_list_cubit.dart';
import '../../../domain/entities/pet_response.dart';
import '../../../core/di/dependency_injection.dart';

class PetFormView extends StatelessWidget {
  final ThemeController themeController;
  final NavigationController? navigationController;
  final VoidCallback? onPetSaved;
  final PetResponse? petToEdit;
  final bool isEditMode;

  const PetFormView({
    Key? key,
    required this.themeController,
    this.navigationController,
    this.onPetSaved,
    this.petToEdit,
  })  : isEditMode = petToEdit != null,
        super(key: key);

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        if (isEditMode)
          BlocProvider(
            create: (context) => EditPetCubit()..initializeWithPet(petToEdit!),
          )
        else
          BlocProvider(
            create: (context) => AddPetCubit(),
          ),
        BlocProvider(
          create: (context) => ThemeCubit(),
        ),
        BlocProvider(
          create: (context) => PetOperationCubit(
            navigationController: navigationController,
            petCacheCubit: getIt<PetCacheCubit>(),
            petListCubit: getIt<PetListCubit>(),
          ),
        ),
      ],
      child: PetForm(
        themeController: themeController,
        navigationController: navigationController,
        onPetSaved: onPetSaved,
        petToEdit: petToEdit,
        isEditMode: isEditMode,
      ),
    );
  }
}

class PetForm extends StatefulWidget {
  final ThemeController themeController;
  final NavigationController? navigationController;
  final VoidCallback? onPetSaved;
  final PetResponse? petToEdit;
  final bool isEditMode;

  const PetForm({
    Key? key,
    required this.themeController,
    this.navigationController,
    this.onPetSaved,
    this.petToEdit,
    this.isEditMode = false,
  }) : super(key: key);

  @override
  State<PetForm> createState() => _PetFormState();
}

class _PetFormState extends State<PetForm> {
  final _petNameController = TextEditingController();
  final _passportController = TextEditingController();
  final _microchipController = TextEditingController();

  @override
  void initState() {
    super.initState();
    // Pre-populate form if editing
    if (widget.isEditMode && widget.petToEdit != null) {
      final pet = widget.petToEdit!;
      _petNameController.text = pet.petName;
      _passportController.text = pet.passportnumber ?? '';
      _microchipController.text = pet.microShipNumber ?? '';
    }
  }

  @override
  void dispose() {
    _petNameController.dispose();
    _passportController.dispose();
    _microchipController.dispose();
    super.dispose();
  }

  void _syncControllersWithState(dynamic formState) {
    if (formState is AddPetFormState) {
      if (_petNameController.text != formState.petName) {
        _petNameController.text = formState.petName;
      }
      if (_passportController.text != formState.passportNumber) {
        _passportController.text = formState.passportNumber;
      }
      if (_microchipController.text != formState.microchipNumber) {
        _microchipController.text = formState.microchipNumber;
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeCubit, ThemeState>(
      builder: (context, themeState) {
        final isDark = themeState is ThemeLoaded ? themeState.isDark : true;

        return Scaffold(
          backgroundColor: isDark ? Colors.black : Colors.white,
          appBar: AppBar(
            backgroundColor: isDark ? Colors.black : Colors.white,
            centerTitle: true,
            title: Text(
              widget.isEditMode ? 'Edit Pet' : 'Add Pet',
              style: TextStyle(
                color: isDark ? Colors.white : Colors.black,
                fontWeight: FontWeight.bold,
              ),
            ),
            leading: IconButton(
              icon: Icon(
                Icons.arrow_back,
                color: isDark ? Colors.white : Colors.black,
              ),
              onPressed: () => Navigator.pop(context),
            ),
          ),
          body: widget.isEditMode ? _buildEditForm(isDark) : _buildAddForm(isDark),
        );
      },
    );
  }

  Widget _buildAddForm(bool isDark) {
    return MultiBlocListener(
      listeners: [
        BlocListener<AddPetCubit, AddPetState>(
          listener: (context, state) {
            if (state is AddPetError) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(state.message),
                  backgroundColor: Colors.red,
                ),
              );
            }
          },
        ),
        BlocListener<PetOperationCubit, PetOperationState>(
          listener: (context, state) {
            if (state is PetOperationSuccess && state.operationType == 'create') {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(state.message),
                  backgroundColor: Colors.green,
                ),
              );
              Navigator.pop(context, true);
            } else if (state is PetOperationError) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(state.message),
                  backgroundColor: Colors.red,
                ),
              );
            }
          },
        ),
      ],
      child: BlocBuilder<PetOperationCubit, PetOperationState>(
        builder: (context, operationState) {
          return BlocBuilder<AddPetCubit, AddPetState>(
            builder: (context, addState) {
              if (addState is AddPetLoading || operationState is PetOperationLoading) {
                return Scaffold(
                  backgroundColor: isDark ? Colors.black : Colors.white,
                  body: const Center(child: CircularProgressIndicator()),
                );
              }

              if (addState is AddPetFormState) {
                _syncControllersWithState(addState);
              }

              return SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    _buildComprehensiveFormFields(context, isDark, isAdd: true),
                    const SizedBox(height: 32),
                    _buildSubmitButton(isDark, isAdd: true),
                  ],
                ),
              );
            },
          );
        },
      ),
    );
  }

  Widget _buildEditForm(bool isDark) {
    return MultiBlocListener(
      listeners: [
        BlocListener<EditPetCubit, EditPetState>(
          listener: (context, state) {
            if (state is EditPetError) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(state.message),
                  backgroundColor: Colors.red,
                ),
              );
            }
          },
        ),
        BlocListener<PetOperationCubit, PetOperationState>(
          listener: (context, state) {
            if (state is PetOperationSuccess && state.operationType == 'update') {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(state.message),
                  backgroundColor: Colors.green,
                ),
              );
              Navigator.pop(context, true);
            } else if (state is PetOperationError) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(state.message),
                  backgroundColor: Colors.red,
                ),
              );
            }
          },
        ),
      ],
      child: BlocBuilder<PetOperationCubit, PetOperationState>(
        builder: (context, operationState) {
          return BlocBuilder<EditPetCubit, EditPetState>(
            builder: (context, editState) {
              if (editState is EditPetLoading || operationState is PetOperationLoading) {
                return Scaffold(
                  backgroundColor: isDark ? Colors.black : Colors.white,
                  body: const Center(child: CircularProgressIndicator()),
                );
              }

              return SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    _buildComprehensiveFormFields(context, isDark, isAdd: false),
                    const SizedBox(height: 32),
                    _buildSubmitButton(isDark, isAdd: false),
                  ],
                ),
              );
            },
          );
        },
      ),
    );
  }

  Widget _buildComprehensiveFormFields(BuildContext context, bool isDark, {required bool isAdd}) {
    if (isAdd) {
      return BlocBuilder<AddPetCubit, AddPetState>(
        builder: (context, state) {
          if (state is! AddPetFormState) {
            return const SizedBox.shrink();
          }

          final formState = state;
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Pet Image Section
              _buildImageSection(context, isDark, formState.petImage, isAdd: true),
              const SizedBox(height: 24),

              // Pet Name Field
              _buildTextField(
                controller: _petNameController,
                label: 'Pet Name *',
                isDark: isDark,
                onChanged: (value) => context.read<AddPetCubit>().updatePetName(value),
              ),
              const SizedBox(height: 16),

              // Species Dropdown
              _buildSpeciesDropdown(context, isDark, formState.selectedSpecies, isAdd: true),
              const SizedBox(height: 16),

              // Breed Dropdown
              _buildBreedDropdown(context, isDark, formState.selectedBreed, formState.selectedSpecies, isAdd: true),
              const SizedBox(height: 16),

              // Gender Selection
              _buildGenderSelection(context, isDark, formState.selectedGender, isAdd: true),
              const SizedBox(height: 16),

              // Birth Date Picker
              _buildDatePicker(context, isDark, formState.selectedDate, isAdd: true),
              const SizedBox(height: 16),

              // Spayed/Neutered Toggle
              _buildSpayedToggle(context, isDark, formState.isSpayed, isAdd: true),
              const SizedBox(height: 16),

              // Passport Number Field
              _buildTextField(
                controller: _passportController,
                label: 'Passport Number (Optional)',
                isDark: isDark,
                onChanged: (value) => context.read<AddPetCubit>().updatePassportNumber(value),
              ),
              const SizedBox(height: 16),

              // Microchip Number Field
              _buildTextField(
                controller: _microchipController,
                label: 'Microchip Number (Optional)',
                isDark: isDark,
                onChanged: (value) => context.read<AddPetCubit>().updateMicrochipNumber(value),
              ),
            ],
          );
        },
      );
    } else {
      // Edit mode - use EditPetCubit
      return BlocBuilder<EditPetCubit, EditPetState>(
        builder: (context, state) {
          if (state is! EditPetFormState) {
            return const SizedBox.shrink();
          }

          final formState = state;
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Pet Name Field
              _buildTextField(
                controller: _petNameController,
                label: 'Pet Name *',
                isDark: isDark,
                onChanged: (value) => context.read<EditPetCubit>().updatePetName(value),
              ),
              const SizedBox(height: 16),

              // Species Dropdown
              _buildSpeciesDropdown(context, isDark, formState.selectedSpecies, isAdd: false),
              const SizedBox(height: 16),

              // Breed Dropdown
              _buildBreedDropdown(context, isDark, formState.selectedBreed, formState.selectedSpecies, isAdd: false),
              const SizedBox(height: 16),

              // Gender Selection
              _buildGenderSelection(context, isDark, formState.selectedGender, isAdd: false),
              const SizedBox(height: 16),

              // Birth Date Picker
              _buildDatePicker(context, isDark, formState.selectedDate, isAdd: false),
              const SizedBox(height: 16),

              // Spayed/Neutered Toggle
              _buildSpayedToggle(context, isDark, formState.isSpayed, isAdd: false),
              const SizedBox(height: 16),

              // Passport Number Field
              _buildTextField(
                controller: _passportController,
                label: 'Passport Number (Optional)',
                isDark: isDark,
                onChanged: (value) => context.read<EditPetCubit>().updatePassportNumber(value),
              ),
              const SizedBox(height: 16),

              // Microchip Number Field
              _buildTextField(
                controller: _microchipController,
                label: 'Microchip Number (Optional)',
                isDark: isDark,
                onChanged: (value) => context.read<EditPetCubit>().updateMicrochipNumber(value),
              ),
            ],
          );
        },
      );
    }
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required bool isDark,
    required Function(String) onChanged,
  }) {
    return TextField(
      controller: controller,
      style: TextStyle(color: isDark ? Colors.white : Colors.black),
      decoration: InputDecoration(
        labelText: label,
        labelStyle: TextStyle(color: isDark ? Colors.grey[300] : Colors.grey[700]),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: isDark ? Colors.grey[600]! : Colors.grey[300]!),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: Colors.blue),
        ),
      ),
      onChanged: onChanged,
    );
  }

  Widget _buildImageSection(BuildContext context, bool isDark, File? petImage, {required bool isAdd}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Pet Photo',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: isDark ? Colors.white : Colors.black87,
          ),
        ),
        const SizedBox(height: 8),
        GestureDetector(
          onTap: () => _showImagePicker(context, isAdd),
          child: Container(
            width: double.infinity,
            height: 200,
            decoration: BoxDecoration(
              color: isDark ? Colors.grey[800] : Colors.grey[100],
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: isDark ? Colors.grey[600]! : Colors.grey[300]!,
                style: BorderStyle.solid,
                width: 1,
              ),
            ),
            child: petImage != null
                ? ClipRRect(
                    borderRadius: BorderRadius.circular(12),
                    child: Image.file(
                      petImage,
                      fit: BoxFit.cover,
                    ),
                  )
                : Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.add_a_photo,
                        size: 48,
                        color: isDark ? Colors.grey[400] : Colors.grey[600],
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Tap to add photo',
                        style: TextStyle(
                          color: isDark ? Colors.grey[400] : Colors.grey[600],
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
          ),
        ),
      ],
    );
  }

  void _showImagePicker(BuildContext context, bool isAdd) {
    showModalBottomSheet(
      context: context,
      builder: (BuildContext context) {
        return SafeArea(
          child: Wrap(
            children: [
              ListTile(
                leading: const Icon(Icons.photo_library),
                title: const Text('Photo Library'),
                onTap: () {
                  Navigator.of(context).pop();
                  _pickImage(ImageSource.gallery, isAdd);
                },
              ),
              ListTile(
                leading: const Icon(Icons.photo_camera),
                title: const Text('Camera'),
                onTap: () {
                  Navigator.of(context).pop();
                  _pickImage(ImageSource.camera, isAdd);
                },
              ),
            ],
          ),
        );
      },
    );
  }

  Future<void> _pickImage(ImageSource source, bool isAdd) async {
    final picker = ImagePicker();
    final pickedFile = await picker.pickImage(source: source);

    if (pickedFile != null) {
      final imageFile = File(pickedFile.path);
      if (isAdd) {
        context.read<AddPetCubit>().updatePetImage(imageFile);
      }
    }
  }

  Widget _buildSpeciesDropdown(BuildContext context, bool isDark, String? selectedSpecies, {required bool isAdd}) {
    // Get available species from the appropriate cubit
    List<String> availableSpecies = [];
    if (isAdd) {
      final addCubit = context.read<AddPetCubit>();
      availableSpecies = addCubit.getAvailableSpecies();
    } else {
      final currentState = context.read<EditPetCubit>().state;
      if (currentState is EditPetFormState) {
        availableSpecies = currentState.availableSpecies;
      }
    }

    // Ensure the selected species is in the available species list
    String? validSelectedSpecies = selectedSpecies;
    if (selectedSpecies != null && !availableSpecies.contains(selectedSpecies)) {
      validSelectedSpecies = availableSpecies.isNotEmpty ? availableSpecies.first : null;
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Species *',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: isDark ? Colors.white : Colors.black87,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
          decoration: BoxDecoration(
            border: Border.all(color: isDark ? Colors.grey[600]! : Colors.grey[300]!),
            borderRadius: BorderRadius.circular(12),
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<String>(
              value: validSelectedSpecies,
              hint: Text(
                'Select Species',
                style: TextStyle(color: isDark ? Colors.grey[400] : Colors.grey[600]),
              ),
              dropdownColor: isDark ? Colors.grey[800] : Colors.white,
              style: TextStyle(color: isDark ? Colors.white : Colors.black),
              items: availableSpecies.map((String species) {
                return DropdownMenuItem<String>(
                  value: species,
                  child: Text(species),
                );
              }).toList(),
              onChanged: availableSpecies.isEmpty ? null : (String? newValue) {
                if (newValue != null) {
                  if (isAdd) {
                    context.read<AddPetCubit>().updateSpecies(newValue);
                  } else {
                    context.read<EditPetCubit>().updateSpecies(newValue);
                  }
                }
              },
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildBreedDropdown(BuildContext context, bool isDark, String? selectedBreed, String? selectedSpecies, {required bool isAdd}) {
    // Get available breeds from the appropriate cubit
    List<String> availableBreeds = [];
    if (isAdd) {
      final addCubit = context.read<AddPetCubit>();
      availableBreeds = addCubit.getBreedsByCurrentSpecies();
    } else {
      final currentState = context.read<EditPetCubit>().state;
      if (currentState is EditPetFormState) {
        availableBreeds = currentState.availableBreeds;
      }
    }

    // Ensure the selected breed is in the available breeds list
    String? validSelectedBreed = selectedBreed;
    if (selectedBreed != null && !availableBreeds.contains(selectedBreed)) {
      validSelectedBreed = availableBreeds.isNotEmpty ? availableBreeds.first : null;
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Breed *',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: isDark ? Colors.white : Colors.black87,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
          decoration: BoxDecoration(
            border: Border.all(color: isDark ? Colors.grey[600]! : Colors.grey[300]!),
            borderRadius: BorderRadius.circular(12),
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<String>(
              value: validSelectedBreed,
              hint: Text(
                selectedSpecies == null ? 'Select Species First' : 'Select Breed',
                style: TextStyle(color: isDark ? Colors.grey[400] : Colors.grey[600]),
              ),
              dropdownColor: isDark ? Colors.grey[800] : Colors.white,
              style: TextStyle(color: isDark ? Colors.white : Colors.black),
              items: availableBreeds.map((String breed) {
                return DropdownMenuItem<String>(
                  value: breed,
                  child: Text(breed),
                );
              }).toList(),
              onChanged: availableBreeds.isEmpty ? null : (String? newValue) {
                if (newValue != null) {
                  if (isAdd) {
                    context.read<AddPetCubit>().updateBreed(newValue);
                  } else {
                    context.read<EditPetCubit>().updateBreed(newValue);
                  }
                }
              },
            ),
          ),
        ),
      ],
    );
  }



  Widget _buildGenderSelection(BuildContext context, bool isDark, String? selectedGender, {required bool isAdd}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Gender *',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: isDark ? Colors.white : Colors.black87,
          ),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: GestureDetector(
                onTap: () {
                  if (isAdd) {
                    context.read<AddPetCubit>().updateGender('Male');
                  } else {
                    context.read<EditPetCubit>().updateGender('Male');
                  }
                },
                child: Container(
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  decoration: BoxDecoration(
                    color: selectedGender == 'Male' ? Colors.blue : (isDark ? Colors.grey[800] : Colors.grey[100]),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: selectedGender == 'Male' ? Colors.blue : (isDark ? Colors.grey[600]! : Colors.grey[300]!),
                    ),
                  ),
                  child: Text(
                    'Male',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      color: selectedGender == 'Male' ? Colors.white : (isDark ? Colors.white : Colors.black),
                      fontWeight: selectedGender == 'Male' ? FontWeight.bold : FontWeight.normal,
                    ),
                  ),
                ),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: GestureDetector(
                onTap: () {
                  if (isAdd) {
                    context.read<AddPetCubit>().updateGender('Female');
                  } else {
                    context.read<EditPetCubit>().updateGender('Female');
                  }
                },
                child: Container(
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  decoration: BoxDecoration(
                    color: selectedGender == 'Female' ? Colors.blue : (isDark ? Colors.grey[800] : Colors.grey[100]),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: selectedGender == 'Female' ? Colors.blue : (isDark ? Colors.grey[600]! : Colors.grey[300]!),
                    ),
                  ),
                  child: Text(
                    'Female',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      color: selectedGender == 'Female' ? Colors.white : (isDark ? Colors.white : Colors.black),
                      fontWeight: selectedGender == 'Female' ? FontWeight.bold : FontWeight.normal,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildDatePicker(BuildContext context, bool isDark, DateTime? selectedDate, {required bool isAdd}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Birth Date (Optional)',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: isDark ? Colors.white : Colors.black87,
          ),
        ),
        const SizedBox(height: 8),
        GestureDetector(
          onTap: () => _selectDate(context, selectedDate, isAdd),
          child: Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
            decoration: BoxDecoration(
              border: Border.all(color: isDark ? Colors.grey[600]! : Colors.grey[300]!),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  selectedDate != null
                      ? '${selectedDate.day}/${selectedDate.month}/${selectedDate.year}'
                      : 'Select Birth Date',
                  style: TextStyle(
                    color: selectedDate != null
                        ? (isDark ? Colors.white : Colors.black)
                        : (isDark ? Colors.grey[400] : Colors.grey[600]),
                    fontSize: 16,
                  ),
                ),
                Icon(
                  Icons.calendar_today,
                  color: isDark ? Colors.grey[400] : Colors.grey[600],
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Future<void> _selectDate(BuildContext context, DateTime? currentDate, bool isAdd) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: currentDate ?? DateTime.now(),
      firstDate: DateTime(2000),
      lastDate: DateTime.now(),
    );
    if (picked != null && picked != currentDate) {
      if (isAdd) {
        context.read<AddPetCubit>().updateDate(picked);
      } else {
        context.read<EditPetCubit>().updateBirthdate(picked);
      }
    }
  }

  Widget _buildSpayedToggle(BuildContext context, bool isDark, bool isSpayed, {required bool isAdd}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Spayed/Neutered',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: isDark ? Colors.white : Colors.black87,
          ),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: GestureDetector(
                onTap: () {
                  if (isAdd) {
                    context.read<AddPetCubit>().updateSpayedStatus(true);
                  } else {
                    context.read<EditPetCubit>().updateSpayedStatus(true);
                  }
                },
                child: Container(
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  decoration: BoxDecoration(
                    color: isSpayed ? Colors.green : (isDark ? Colors.grey[800] : Colors.grey[100]),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: isSpayed ? Colors.green : (isDark ? Colors.grey[600]! : Colors.grey[300]!),
                    ),
                  ),
                  child: Text(
                    'Spayed/Neutered',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      color: isSpayed ? Colors.white : (isDark ? Colors.white : Colors.black),
                      fontWeight: isSpayed ? FontWeight.bold : FontWeight.normal,
                    ),
                  ),
                ),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: GestureDetector(
                onTap: () {
                  if (isAdd) {
                    context.read<AddPetCubit>().updateSpayedStatus(false);
                  } else {
                    context.read<EditPetCubit>().updateSpayedStatus(false);
                  }
                },
                child: Container(
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  decoration: BoxDecoration(
                    color: !isSpayed ? Colors.orange : (isDark ? Colors.grey[800] : Colors.grey[100]),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: !isSpayed ? Colors.orange : (isDark ? Colors.grey[600]! : Colors.grey[300]!),
                    ),
                  ),
                  child: Text(
                    'Not Spayed',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      color: !isSpayed ? Colors.white : (isDark ? Colors.white : Colors.black),
                      fontWeight: !isSpayed ? FontWeight.bold : FontWeight.normal,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildSubmitButton(bool isDark, {required bool isAdd}) {
    return SizedBox(
      width: double.infinity,
      child: isAdd
        ? BlocBuilder<AddPetCubit, AddPetState>(
            builder: (context, state) {
              final isFormValid = state is AddPetFormState ? state.isFormValid : false;
              return ElevatedButton(
                onPressed: isFormValid ? () {
                  final addCubit = context.read<AddPetCubit>();
                  final createRequest = addCubit.buildCreateRequest();
                  if (createRequest != null) {
                    context.read<PetOperationCubit>().createPet(createRequest);
                  }
                } : null,
                style: ElevatedButton.styleFrom(
                  backgroundColor: isFormValid ? Colors.blue : Colors.grey,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: Text(
                  isFormValid ? 'Add Pet' : 'Fill Required Fields',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              );
            },
          )
        : BlocBuilder<EditPetCubit, EditPetState>(
            builder: (context, state) {
              final isFormValid = state is EditPetFormState ? state.isFormValid : false;
              return ElevatedButton(
                onPressed: isFormValid ? () {
                  final editCubit = context.read<EditPetCubit>();
                  final updateRequest = editCubit.buildUpdateRequest(widget.petToEdit!.id);
                  if (updateRequest != null) {
                    context.read<PetOperationCubit>().updatePet(updateRequest);
                  }
                } : null,
                style: ElevatedButton.styleFrom(
                  backgroundColor: isFormValid ? Colors.blue : Colors.grey,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: Text(
                  isFormValid ? 'Update Pet' : 'Fill Required Fields',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              );
            },
          ),
    );
  }
}
