import '../domain/entities/create_pet_request.dart';
import '../domain/entities/create_pet_response.dart';
import '../domain/entities/update_pet_request.dart';
import '../domain/entities/update_pet_response.dart';
import '../domain/entities/pet_response.dart';
import '../domain/usecases/create_pet_usecase.dart';
import '../domain/usecases/update_pet_usecase.dart';
import '../domain/usecases/get_all_pets_usecase.dart';
import '../domain/usecases/delete_pet_usecase.dart';

class PetService {
  final CreatePetUseCase _createPetUseCase;
  final UpdatePetUseCase _updatePetUseCase;
  final GetAllPetsUseCase _getAllPetsUseCase;
  final DeletePetUseCase _deletePetUseCase;

  PetService({
    required CreatePetUseCase createPetUseCase,
    required UpdatePetUseCase updatePetUseCase,
    required GetAllPetsUseCase getAllPetsUseCase,
    required DeletePetUseCase deletePetUseCase,
  })  : _createPetUseCase = createPetUseCase,
        _updatePetUseCase = updatePetUseCase,
        _getAllPetsUseCase = getAllPetsUseCase,
        _deletePetUseCase = deletePetUseCase;

  Future<CreatePetResponse> createPet(CreatePetRequest request) async {
    return await _createPetUseCase(request);
  }

  Future<UpdatePetResponse> updatePet(UpdatePetRequest request) async {
    return await _updatePetUseCase(request);
  }

  Future<List<PetResponse>> getAllPets() async {
    return await _getAllPetsUseCase();
  }

  Future<bool> deletePet(String petId, String petName) async {
    return await _deletePetUseCase(petId, petName);
  }
}
