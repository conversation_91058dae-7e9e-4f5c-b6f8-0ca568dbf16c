import '../entities/create_pet_request.dart';
import '../entities/create_pet_response.dart';
import '../repositories/pet_repository.dart';

class CreatePetUseCase {
  final PetRepository repository;

  CreatePetUseCase(this.repository);

  Future<CreatePetResponse> call(CreatePetRequest request) async {
    try {
      print('🐾 Creating pet: ${request.petName}');
      print('📋 Pet details: Species ID: ${request.specieId}, Breed ID: ${request.breedId}');
      
      final response = await repository.createPet(request);
      
      if (response.success) {
        print('✅ Pet created successfully: ${request.petName}');
      } else {
        print('❌ Failed to create pet: ${response.message ?? 'Unknown error'}');
        if (response.errors != null) {
          print('🔍 Errors: ${response.errors}');
        }
      }
      
      return response;
    } catch (e) {
      print('❌ Error in CreatePetUseCase: $e');
      rethrow;
    }
  }
}
