import 'package:flutter/material.dart';
import '../../controllers/home/<USER>';
import '../../controllers/theme_controller.dart';

class CustomAppBar extends StatelessWidget implements PreferredSizeWidget {
  final NavigationController navigationController;
  final ThemeController themeController;
  
  const CustomAppBar({
    Key? key,
    required this.navigationController,
    required this.themeController,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AppBar(
      automaticallyImplyLeading: false,
      centerTitle: true,
      title: ListenableBuilder(
        listenable: navigationController,
        builder: (context, _) {
          return Text(
            _getAppBarTitle(),
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              letterSpacing: 1.2,
              color: Colors.white,
              shadows: [
                Shadow(
                  blurRadius: 8,
                  color: Colors.black26,
                  offset: Offset(0, 2),
                ),
              ],
            ),
          );
        },
      ),
      flexibleSpace: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: themeController.isDark
                ? [const Color(0xFF232526), const Color(0xFF414345)]
                : [const Color(0xFF6D5DF6), const Color(0xFF46A0FC)],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: const BorderRadius.vertical(
            bottom: Radius.circular(24),
          ),
          boxShadow: const [
            BoxShadow(
              color: Colors.black26,
              blurRadius: 12,
              offset: Offset(0, 4),
            ),
          ],
        ),
      ),
      elevation: 8,
    );
  }

  String _getAppBarTitle() {
    switch (navigationController.currentTab) {
      case NavigationTab.settings:
        return '⚙️ Settings';
      case NavigationTab.history:
        return '🕐 History';
      case NavigationTab.pets:
        return '🐶 Pets';
      case NavigationTab.addUser:
        return '👤 Add User';
      case NavigationTab.home:
        return '🏠 Home';
    }
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
} 