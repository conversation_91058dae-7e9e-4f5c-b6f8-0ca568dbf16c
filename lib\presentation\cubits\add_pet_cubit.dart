import 'dart:io';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import '../../core/di/dependency_injection.dart';
import '../../domain/entities/create_pet_response.dart';
import '../../domain/entities/create_pet_request.dart';
import '../../services/pet_service.dart';
import '../../services/pet_data_service.dart';

// States
abstract class AddPetState extends Equatable {
  const AddPetState();

  @override
  List<Object?> get props => [];
}

class AddPetInitial extends AddPetState {}

class AddPetLoading extends AddPetState {}

class AddPetSuccess extends AddPetState {
  final CreatePetResponse response;

  const AddPetSuccess(this.response);

  @override
  List<Object?> get props => [response];
}

class AddPetError extends AddPetState {
  final String message;

  const AddPetError(this.message);

  @override
  List<Object?> get props => [message];
}

class AddPetFormState extends AddPetState {
  final String petName;
  final String selectedSpecies;
  final String selectedBreed;
  final String selectedGender;
  final DateTime selectedDate;
  final bool isSpayed;
  final File? petImage;
  final File? passportImage;
  final String passportNumber;
  final String microchipNumber;

  const AddPetFormState({
    this.petName = '',
    this.selectedSpecies = 'Dog',
    this.selectedBreed = 'Other',
    this.selectedGender = 'Male',
    required this.selectedDate,
    this.isSpayed = false,
    this.petImage,
    this.passportImage,
    this.passportNumber = '',
    this.microchipNumber = '',
  });

  AddPetFormState copyWith({
    String? petName,
    String? selectedSpecies,
    String? selectedBreed,
    String? selectedGender,
    DateTime? selectedDate,
    bool? isSpayed,
    File? petImage,
    File? passportImage,
    String? passportNumber,
    String? microchipNumber,
    bool clearPetImage = false,
    bool clearPassportImage = false,
  }) {
    return AddPetFormState(
      petName: petName ?? this.petName,
      selectedSpecies: selectedSpecies ?? this.selectedSpecies,
      selectedBreed: selectedBreed ?? this.selectedBreed,
      selectedGender: selectedGender ?? this.selectedGender,
      selectedDate: selectedDate ?? this.selectedDate,
      isSpayed: isSpayed ?? this.isSpayed,
      petImage: clearPetImage ? null : (petImage ?? this.petImage),
      passportImage: clearPassportImage ? null : (passportImage ?? this.passportImage),
      passportNumber: passportNumber ?? this.passportNumber,
      microchipNumber: microchipNumber ?? this.microchipNumber,
    );
  }

  // Validation method - checks if all required fields are filled
  bool get isFormValid {
    return petName.trim().isNotEmpty &&
           selectedSpecies.isNotEmpty &&
           selectedBreed.isNotEmpty &&
           selectedGender.isNotEmpty;
  }

  @override
  List<Object?> get props => [
        petName,
        selectedSpecies,
        selectedBreed,
        selectedGender,
        selectedDate,
        isSpayed,
        petImage,
        passportImage,
        passportNumber,
        microchipNumber,
      ];
}

// Cubit
class AddPetCubit extends Cubit<AddPetState> {
  final PetService _petService;
  final PetDataService _petDataService;

  AddPetCubit({
    PetService? petService,
    PetDataService? petDataService,
  })  : _petService = petService ?? getIt<PetService>(),
        _petDataService = petDataService ?? getIt<PetDataService>(),
        super(AddPetFormState(selectedDate: DateTime.now()));

  void updatePetName(String name) {
    if (state is AddPetFormState) {
      emit((state as AddPetFormState).copyWith(petName: name));
    }
  }

  void updateSpecies(String species) {
    if (state is AddPetFormState) {
      final currentState = state as AddPetFormState;
      final breeds = _getBreedsBySpecies(species);
      emit(currentState.copyWith(
        selectedSpecies: species,
        selectedBreed: breeds.isNotEmpty ? breeds[0] : 'Other',
      ));
    }
  }

  void updateBreed(String breed) {
    if (state is AddPetFormState) {
      emit((state as AddPetFormState).copyWith(selectedBreed: breed));
    }
  }

  void updateGender(String gender) {
    if (state is AddPetFormState) {
      emit((state as AddPetFormState).copyWith(selectedGender: gender));
    }
  }

  void updateDate(DateTime date) {
    if (state is AddPetFormState) {
      emit((state as AddPetFormState).copyWith(selectedDate: date));
    }
  }

  void updateSpayedStatus(bool isSpayed) {
    if (state is AddPetFormState) {
      emit((state as AddPetFormState).copyWith(isSpayed: isSpayed));
    }
  }

  void updatePetImage(File? image) {
    if (state is AddPetFormState) {
      emit((state as AddPetFormState).copyWith(
        petImage: image,
        clearPetImage: image == null,
      ));
    }
  }

  void updatePassportImage(File? image) {
    if (state is AddPetFormState) {
      emit((state as AddPetFormState).copyWith(
        passportImage: image,
        clearPassportImage: image == null,
      ));
    }
  }

  void updatePassportNumber(String number) {
    if (state is AddPetFormState) {
      emit((state as AddPetFormState).copyWith(passportNumber: number));
    }
  }

  void updateMicrochipNumber(String number) {
    if (state is AddPetFormState) {
      emit((state as AddPetFormState).copyWith(microchipNumber: number));
    }
  }

  List<String> _getBreedsBySpecies(String species) {
    final breeds = _petDataService.getBreedsBySpeciesName(species);
    final breedNames = breeds.map((breed) => breed.name).toList();
    if (!breedNames.contains('Other')) {
      breedNames.add('Other');
    }
    return breedNames;
  }

  List<String> getBreedsByCurrentSpecies() {
    if (state is AddPetFormState) {
      return _getBreedsBySpecies((state as AddPetFormState).selectedSpecies);
    }
    return [];
  }

  List<String> getAvailableSpecies() {
    return _petDataService.getAvailableSpecies();
  }

  CreatePetRequest? buildCreateRequest() {
    if (state is! AddPetFormState) return null;

    final formState = state as AddPetFormState;

    // Check if all required fields are filled
    if (!formState.isFormValid) {
      return null;
    }

    try {
      // Get species and breed IDs
      final speciesId = _petDataService.getSpeciesIdByName(formState.selectedSpecies);
      final breedId = _petDataService.getBreedIdByName(formState.selectedBreed);

      if (speciesId == null) {
        emit(const AddPetError('Invalid species selected'));
        return null;
      }

      if (breedId == null) {
        emit(const AddPetError('Invalid breed selected'));
        return null;
      }

      // Convert gender string to int (0 = Male, 1 = Female)
      final genderInt = formState.selectedGender.toLowerCase() == 'male' ? 0 : 1;

      // Create the pet request
      return CreatePetRequest(
        petName: formState.petName.trim(),
        gender: genderInt,
        breedId: breedId,
        specieId: speciesId,
        birthdate: formState.selectedDate,
        isSpayed: formState.isSpayed,
        passportnumber: formState.passportNumber.trim().isEmpty ? null : formState.passportNumber.trim(),
        microShipNumber: formState.microchipNumber.trim().isEmpty ? null : formState.microchipNumber.trim(),
      );
    } catch (e) {
      emit(AddPetError('Failed to build create request: ${e.toString()}'));
      return null;
    }
  }

  Future<void> savePet() async {
    if (state is! AddPetFormState) return;

    final formState = state as AddPetFormState;

    // Check if all required fields are filled
    if (!formState.isFormValid) {
      emit(const AddPetError('Please fill in all required fields (Pet Name, Species, Breed, Gender)'));
      return;
    }

    emit(AddPetLoading());

    try {
      // Get species and breed IDs
      final speciesId = _petDataService.getSpeciesIdByName(formState.selectedSpecies);
      final breedId = _petDataService.getBreedIdByName(formState.selectedBreed);

      if (speciesId == null) {
        emit(const AddPetError('Invalid species selected'));
        return;
      }

      if (breedId == null) {
        emit(const AddPetError('Invalid breed selected'));
        return;
      }

      // Convert gender string to int (0 = Male, 1 = Female)
      final genderInt = formState.selectedGender.toLowerCase() == 'male' ? 0 : 1;

      // Create the pet request
      final request = CreatePetRequest(
        petName: formState.petName.trim(),
        gender: genderInt,
        breedId: breedId,
        specieId: speciesId,
        birthdate: formState.selectedDate,
        isSpayed: formState.isSpayed,
        passportnumber: formState.passportNumber.trim().isEmpty ? null : formState.passportNumber.trim(),
        microShipNumber: formState.microchipNumber.trim().isEmpty ? null : formState.microchipNumber.trim(),
      );

      final response = await _petService.createPet(request);
      emit(AddPetSuccess(response));
    } catch (e) {
      emit(AddPetError('Failed to create pet: ${e.toString()}'));
    }
  }

  void resetForm() {
    emit(AddPetFormState(selectedDate: DateTime.now()));
  }

  void clearError() {
    if (state is AddPetError) {
      emit(AddPetFormState(selectedDate: DateTime.now()));
    }
  }
}
