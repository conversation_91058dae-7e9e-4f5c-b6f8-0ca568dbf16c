import 'package:flutter_test/flutter_test.dart';
import 'package:first_app/core/di/dependency_injection.dart';
import 'package:first_app/services/pet_data_service.dart';
import 'package:first_app/domain/entities/pet_data.dart';

void main() {
  setUpAll(() async {
    TestWidgetsFlutterBinding.ensureInitialized();
    await DependencyInjection.init();
  });

  group('PetDataService Tests', () {
    test('should load species and breeds successfully', () async {
      // Arrange
      final service = getIt<PetDataService>();
      
      // Act
      await service.loadAllPetData();
      final species = await service.getSpecies();
      final breeds = await service.getBreeds();
      
      // Assert
      expect(species, isNotEmpty, reason: 'Species list should not be empty');
      expect(breeds, isNotEmpty, reason: 'Breeds list should not be empty');
      expect(PetData.species, isNotEmpty, reason: 'PetData species should be updated');
      
      print('✅ Test passed: Loaded ${species.length} species and ${breeds.length} breeds');
      print('📊 PetData now has ${PetData.species.length} species categories');
    });

    test('should map species IDs correctly', () async {
      // Arrange
      final service = getIt<PetDataService>();
      await service.loadAllPetData();
      
      // Act
      final dogSpeciesName = service.getSpeciesNameById('bca48207-f05d-4e9f-a631-06f34eb5af39');
      final catSpeciesName = service.getSpeciesNameById('f1131363-3b9f-40ee-9a89-0573ee274a10');
      final unknownSpeciesName = service.getSpeciesNameById('unknown-id');
      
      // Assert
      expect(dogSpeciesName, isNotEmpty, reason: 'Dog species name should be found');
      expect(catSpeciesName, isNotEmpty, reason: 'Cat species name should be found');
      expect(unknownSpeciesName, equals('Other'), reason: 'Unknown ID should return "Other"');
      
      print('✅ Species mapping test passed');
      print('  Dog ID maps to: $dogSpeciesName');
      print('  Cat ID maps to: $catSpeciesName');
      print('  Unknown ID maps to: $unknownSpeciesName');
    });
  });
}
