import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../../../domain/entities/species.dart';

abstract class SpeciesRemoteDataSource {
  Future<List<Species>> getSpecies();
}

class SpeciesRemoteDataSourceImpl implements SpeciesRemoteDataSource {
  final http.Client client;
  static const String baseUrl = 'https://squeakapi.veticareapp.com:8001/v1/api';

  SpeciesRemoteDataSourceImpl({http.Client? client}) : client = client ?? http.Client();

  @override
  Future<List<Species>> getSpecies() async {
    try {
      // Get token from shared preferences
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('token');
      
      if (token == null || token.isEmpty) {
        throw Exception('No authentication token found');
      }

      final url = Uri.parse('$baseUrl/Species');
      
      print('🌐 Making Species API request to: $url');
      print('🔑 Using token: ${token.substring(0, 20)}...');

      final response = await client.get(
        url,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      print('📡 Species API Response Status: ${response.statusCode}');
      print('📡 Species API Response Body: ${response.body}');

      if (response.statusCode == 200) {
        final dynamic jsonResponse = jsonDecode(response.body);
        
        // Handle the API response structure
        List<dynamic> speciesJson;
        if (jsonResponse is Map<String, dynamic>) {
          if (jsonResponse.containsKey('data') && jsonResponse['data'] is Map<String, dynamic>) {
            final data = jsonResponse['data'] as Map<String, dynamic>;
            if (data.containsKey('speciesDtos')) {
              speciesJson = data['speciesDtos'] as List<dynamic>;
            } else if (data.containsKey('species')) {
              speciesJson = data['species'] as List<dynamic>;
            } else {
              throw Exception('No species data found in response');
            }
          } else if (jsonResponse.containsKey('data') && jsonResponse['data'] is List<dynamic>) {
            speciesJson = jsonResponse['data'] as List<dynamic>;
          } else {
            throw Exception('Unexpected response format: Map without proper data structure');
          }
        } else if (jsonResponse is List<dynamic>) {
          speciesJson = jsonResponse;
        } else {
          throw Exception('Unexpected response format: ${jsonResponse.runtimeType}');
        }

        final species = speciesJson.map((json) {
          final speciesMap = json as Map<String, dynamic>;
          return Species(
            id: speciesMap['id'] ?? '',
            enName: speciesMap['enType'] ?? speciesMap['name'] ?? '',
            arName: speciesMap['arType'] ?? speciesMap['enType'] ?? '',
          );
        }).toList();
        
        print('✅ Successfully parsed ${species.length} species');
        return species;
      } else {
        throw Exception('Failed to fetch species: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      print('❌ Error fetching species: $e');
      rethrow;
    }
  }
}
