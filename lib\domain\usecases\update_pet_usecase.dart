import '../entities/update_pet_request.dart';
import '../entities/update_pet_response.dart';
import '../repositories/pet_repository.dart';

class UpdatePetUseCase {
  final PetRepository repository;

  UpdatePetUseCase(this.repository);

  Future<UpdatePetResponse> call(UpdatePetRequest request) async {
    try {
      print('🐾 Updating pet: ${request.petName} (ID: ${request.id})');
      print('📋 Pet details: Species ID: ${request.specieId}, Breed ID: ${request.breedId}');
      
      final response = await repository.updatePet(request);
      
      if (response.success) {
        print('✅ Pet updated successfully: ${request.petName}');
      } else {
        print('❌ Failed to update pet: ${response.message ?? 'Unknown error'}');
        if (response.errors != null) {
          print('🔍 Errors: ${response.errors}');
        }
      }
      
      return response;
    } catch (e) {
      print('❌ Error in UpdatePetUseCase: $e');
      rethrow;
    }
  }
}
