class LoginResponseModel {
  final bool success;
  final Map<String, dynamic> errors;
  final LoginData? data;
  final String message;
  final int statusCode;

  LoginResponseModel({
    required this.success,
    required this.errors,
    required this.data,
    required this.message,
    required this.statusCode,
  });

  factory LoginResponseModel.fromJson(Map<String, dynamic> json) {
    return LoginResponseModel(
      success: json['success'] ?? false,
      errors: json['errors'] ?? {},
      data: json['data'] != null ? LoginData.fromJson(json['data']) : null,
      message: json['message'] ?? '',
      statusCode: json['statusCode'] ?? 0,
    );
  }
}

class LoginData {
  final String id;
  final String email;
  final String fullName;
  final String phone;
  final int role;
  final String token;
  final String refreshToken;
  final String tokenType;
  final String expiresIn;

  LoginData({
    required this.id,
    required this.email,
    required this.fullName,
    required this.phone,
    required this.role,
    required this.token,
    required this.refreshToken,
    required this.tokenType,
    required this.expiresIn,
  });

  factory LoginData.fromJson(Map<String, dynamic> json) {
    return LoginData(
      id: json['id'] ?? '',
      email: json['email'] ?? '',
      fullName: json['fullName'] ?? '',
      phone: json['phone'] ?? '',
      role: json['role'] ?? 0,
      token: json['token'] ?? '',
      refreshToken: json['refreshToken'] ?? '',
      tokenType: json['tokenType'] ?? '',
      expiresIn: json['expiresIn'] ?? '',
    );
  }
} 