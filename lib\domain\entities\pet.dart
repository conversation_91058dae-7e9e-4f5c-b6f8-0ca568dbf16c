class Pet {
  final String id;
  final String name;
  final String species;
  final String breed;
  final String gender;
  final DateTime dateOfBirth;
  final String? microchipNumber;
  final String? passportNumber;
  final bool isSpayed;
  final String? imageUrl;

  Pet({
    required this.id,
    required this.name,
    required this.species,
    required this.breed,
    required this.gender,
    required this.dateOfBirth,
    this.microchipNumber,
    this.passportNumber,
    required this.isSpayed,
    this.imageUrl,
  });

  factory Pet.fromJson(Map<String, dynamic> json) {
    return Pet(
      id: json['id'] as String,
      name: json['name'] as String,
      species: json['species'] as String,
      breed: json['breed'] as String,
      gender: json['gender'] as String,
      dateOfBirth: DateTime.parse(json['dateOfBirth'] as String),
      microchipNumber: json['microchipNumber'] as String?,
      passportNumber: json['passportNumber'] as String?,
      isSpayed: json['isSpayed'] as bool,
      imageUrl: json['imageUrl'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'species': species,
      'breed': breed,
      'gender': gender,
      'dateOfBirth': dateOfBirth.toIso8601String(),
      'microchipNumber': microchipNumber,
      'passportNumber': passportNumber,
      'isSpayed': isSpayed,
      'imageUrl': imageUrl,
    };
  }
}