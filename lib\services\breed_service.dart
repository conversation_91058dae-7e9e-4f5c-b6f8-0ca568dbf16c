import '../domain/entities/breed.dart';
import '../domain/entities/pet_data.dart';
import '../domain/usecases/get_breeds_usecase.dart';

class BreedService {
  final GetBreedsUseCase _getBreedsUseCase;

  BreedService({
    required GetBreedsUseCase getBreedsUseCase,
  }) : _getBreedsUseCase = getBreedsUseCase;

  Future<void> loadAndUpdateBreeds() async {
    try {
      print('🐾 Starting breed loading process...');
      
      final breeds = await _getBreedsUseCase();
      
      if (breeds.isNotEmpty) {
        _updatePetDataWithBreeds(breeds);
        print('✅ Successfully updated PetData with ${breeds.length} breeds');
      } else {
        print('⚠️ No breeds received from API');
      }
    } catch (e) {
      print('❌ Failed to load breeds: $e');
      // Don't throw - let the app continue with default data
    }
  }

  void _updatePetDataWithBreeds(List<Breed> breeds) {
    // Group breeds by species
    final Map<String, List<String>> speciesBreedMap = {};
    
    for (final breed in breeds) {
      final speciesName = breed.species;
      if (!speciesBreedMap.containsKey(speciesName)) {
        speciesBreedMap[speciesName] = [];
      }
      speciesBreedMap[speciesName]!.add(breed.name);
    }

    // Convert to PetSpecies list
    final List<PetSpecies> newSpecies = speciesBreedMap.entries.map((entry) {
      final breedList = entry.value;
      // Add "Other" option if not already present
      if (!breedList.contains('Other')) {
        breedList.add('Other');
      }
      
      return PetSpecies(
        name: entry.key,
        breeds: breedList,
      );
    }).toList();

    // Update PetData
    if (newSpecies.isNotEmpty) {
      PetData.updateSpecies(newSpecies);
      
      // Log the updated data
      print('📊 Updated species data:');
      for (final species in newSpecies) {
        print('  ${species.name}: ${species.breeds.length} breeds');
      }
    }
  }

  Future<List<Breed>> getBreeds() async {
    return await _getBreedsUseCase();
  }
}
