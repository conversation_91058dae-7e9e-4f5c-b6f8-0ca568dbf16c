import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../cubits/pet_list_cubit.dart';
import '../../cubits/pet_actions_cubit.dart';
import '../../cubits/pet_cache_cubit.dart';
import '../../controllers/theme_controller.dart';
import '../../controllers/home/<USER>';
import '../../../domain/entities/pet_response.dart';
import '../../../core/di/dependency_injection.dart';
import 'pet_form_view.dart';

class PetsListView extends StatelessWidget {
  final NavigationController? navigationController;

  const PetsListView({Key? key, this.navigationController}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return BlocProvider(
      create: (context) => PetActionsCubit(
        navigationController: navigationController,
        petCacheCubit: getIt<PetCacheCubit>(),
        petListCubit: context.read<PetListCubit>(),
      ),
      child: Scaffold(
      body: BlocConsumer<PetListCubit, PetListState>(
        listener: (context, state) {
          if (state is PetListError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: Colors.red,
              ),
            );
          }
        },
        builder: (context, state) {
          if (state is PetListLoading) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }

          if (state is PetListError) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 64,
                    color: Colors.red[300],
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Failed to load pets',
                    style: TextStyle(
                      fontSize: 18,
                      color: isDark ? Colors.white : Colors.black87,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    state.message,
                    style: TextStyle(
                      fontSize: 14,
                      color: isDark ? Colors.grey[300] : Colors.grey[600],
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () => context.read<PetListCubit>().loadPets(forceRefresh: true),
                    child: const Text('Retry'),
                  ),
                ],
              ),
            );
          }

          if (state is PetListLoaded || state is PetListDeleting) {
            final pets = state is PetListLoaded ? state.pets : (state as PetListDeleting).pets;
            final deletingPetId = state is PetListDeleting ? state.deletingPetId : null;

            if (pets.isEmpty) {
              return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.pets,
                      size: 64,
                      color: isDark ? Colors.grey[600] : Colors.grey[400],
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'No pets found',
                      style: TextStyle(
                        fontSize: 18,
                        color: isDark ? Colors.white : Colors.black87,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Add your first pet to get started',
                      style: TextStyle(
                        fontSize: 14,
                        color: isDark ? Colors.grey[300] : Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              );
            }

            return RefreshIndicator(
              onRefresh: () => context.read<PetListCubit>().loadPets(forceRefresh: true),
              child: ListView.builder(
                padding: const EdgeInsets.symmetric(vertical: 8),
                itemCount: pets.length,
                itemBuilder: (context, index) {
                  final pet = pets[index];
                  return PetCard(
                    pet: pet,
                    isDeleting: deletingPetId == pet.id,
                    onDelete: () => _showDeleteConfirmation(context, pet.id, pet.petName),
                    onTap: () => _navigateToEditPet(context, pet),
                  );
                },
              ),
            );
          }

          return const SizedBox.shrink();
        },
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () async {
          // Navigate to add pet view and refresh the list when returning
          final result = await Navigator.push(
            context,
            MaterialPageRoute(
              builder: (_) => PetFormView(
                themeController: ThemeController(),
                navigationController: navigationController,
                onPetSaved: () {
                  context.read<PetListCubit>().loadPets(forceRefresh: true);
                },
              ),
            ),
          );

          // Refresh the pets list if a pet was added
          if (result == true) {
            context.read<PetListCubit>().loadPets(forceRefresh: true);
          }
        },
        label: const Text('Add Pet'),
        icon: const Icon(Icons.add),
      ),
    );
  }

  void _navigateToEditPet(BuildContext context, PetResponse pet) async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (_) => PetFormView(
          petToEdit: pet,
          themeController: ThemeController(),
          navigationController: navigationController,
          onPetSaved: () {
            context.read<PetListCubit>().loadPets(forceRefresh: true);
          },
        ),
      ),
    );

    // Refresh the pets list if a pet was updated
    if (result == true) {
      context.read<PetListCubit>().loadPets(forceRefresh: true);
    }
  }

  void _showDeleteConfirmation(BuildContext context, String petId, String petName) {
    // Capture the cubit reference before showing the dialog
    final petListCubit = context.read<PetListCubit>();

    showDialog(
      context: context,
      builder: (dialogContext) => AlertDialog(
        title: const Text('Delete Pet'),
        content: Text('Are you sure you want to delete $petName? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(dialogContext).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(dialogContext).pop();
              petListCubit.deletePet(petId, petName);
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
}
