class CreatePetRequest {
  final String petName;
  final int gender; // 0 = Male, 1 = Female
  final String breedId;
  final String? imageName;
  final DateTime birthdate;
  final String specieId;
  final bool isSpayed;
  final String? passportImage;
  final String? passportnumber;
  final String? microShipNumber;

  const CreatePetRequest({
    required this.petName,
    required this.gender,
    required this.breedId,
    this.imageName,
    required this.birthdate,
    required this.specieId,
    required this.isSpayed,
    this.passportImage,
    this.passportnumber,
    this.microShipNumber,
  });

  Map<String, dynamic> toJson() {
    return {
      'petName': petName,
      'gender': gender,
      'breedId': breedId,
      if (imageName != null) 'imageName': imageName,
      'birthdate': birthdate.toIso8601String(),
      'specieId': specieId,
      'isSpayed': isSpayed,
      if (passportImage != null) 'passportImage': passportImage,
      if (passportnumber != null) 'passportnumber': passportnumber,
      if (microShipNumber != null) 'microShipNumber': microShipNumber,
    };
  }

  @override
  String toString() => 'CreatePetRequest(petName: $petName, gender: $gender, breedId: $breedId, specieId: $specieId)';
}
