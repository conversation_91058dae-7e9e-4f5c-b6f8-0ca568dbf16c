# Pet Data API Integration

This document explains the implementation of both Species and Breed API integration in the Flutter app.

## Overview

The app now fetches both species and breed data from the API endpoints:
- **Species**: `https://squeakapi.veticareapp.com:8001/v1/api/Species`
- **Breeds**: `https://squeakapi.veticareapp.com:8001/v1/api/breed`

Both endpoints use authentication tokens and cache data locally to prevent repeated API calls.

## Architecture

The implementation follows Clean Architecture principles:

### Domain Layer
- **Entities**: `Species`, `Breed`, `PetSpecies`, `PetData`
- **Repositories**: `SpeciesRepository`, `BreedRepository` (interfaces)
- **Use Cases**: `GetSpeciesUseCase`, `GetBreedsUseCase`

### Data Layer
- **Remote Data Sources**: `SpeciesRemoteDataSourceImpl`, `BreedRemoteDataSourceImpl`
- **Local Data Sources**: `SpeciesLocalDataSourceImpl`, `BreedLocalDataSourceImpl`
- **Repository Implementations**: `SpeciesRepositoryImpl`, `BreedRepositoryImpl`

### Presentation Layer
- **Service**: `PetDataService` (singleton) - combines both species and breeds
- **Cubit**: `BreedCubit` for state management
- **UI**: Updated `AddPetView` with loading states

## Features

### 🌐 API Integration
- Fetches breeds from the API using authentication token
- Handles different response formats gracefully
- Maps API response to domain entities
- Comprehensive error handling and logging

### 💾 Caching System
- Stores breed data locally using SharedPreferences
- Cache validity: 24 hours
- Automatic cache invalidation
- Fallback to cached data on API errors

### 🔄 Smart Loading
- Loads from cache first if available and valid
- Falls back to API if cache is empty or expired
- Updates UI with loading indicators
- Refresh functionality in the UI

### 📱 UI Integration
- Loading spinner during API calls
- Refresh button to manually update breeds
- Seamless integration with existing pet form
- Error handling without breaking user experience

## API Response Structures

### Species API Response
```json
{
  "success": true,
  "errors": {},
  "data": {
    "speciesDtos": [
      {
        "id": "f1131363-3b9f-40ee-9a89-0573ee274a10",
        "arType": "Arabic Name",
        "enType": "English Name"
      }
    ]
  }
}
```

### Breeds API Response
```json
{
  "success": true,
  "data": {
    "breedDto": [
      {
        "id": "uuid",
        "enBreed": "Breed Name",
        "arBreed": "Arabic Name",
        "specieId": "species-uuid",
        "specie": null
      }
    ]
  }
}
```

## Species Integration

The app now fetches species data directly from the API and builds a dynamic mapping:
- **112 species** loaded from the API
- Species IDs are mapped to their English names (`enType`)
- Breeds are properly categorized using the species mapping
- Unknown species IDs default to "Other"

### Key Species Examples
- `bca48207-f05d-4e9f-a631-06f34eb5af39` → "Dog"
- `f1131363-3b9f-40ee-9a89-0573ee274a10` → "Cat" (or "coww" based on API data)
- Many other species like Cow, Hippopotamus, Elephant, etc.

## Usage

### Automatic Loading
Both species and breeds are automatically loaded:
1. On app startup (if user is logged in)
2. After successful login
3. When opening the Add Pet screen

The loading process:
1. **Species First**: Loads all species to build ID-to-name mapping
2. **Breeds Second**: Loads breeds and maps them to proper species using the mapping
3. **PetData Update**: Updates the app's species/breed structure with API data

### Manual Refresh
Users can manually refresh both species and breeds by tapping the refresh icon in the species dropdown.

### Console Logging
The implementation provides detailed console logs for both APIs:
- 🌐 API requests and responses (Species & Breeds)
- 💾 Caching operations for both data types
- 📱 Local data operations
- 📋 Species mapping operations
- ❌ Error messages with context
- ✅ Success confirmations with counts

## Error Handling

The system gracefully handles:
- Network connectivity issues
- API authentication errors
- Invalid response formats
- Cache corruption
- Missing data

In all error cases, the app continues to function with default breed data.

## Testing

Run tests with:
```bash
flutter test test/species_service_test.dart
flutter test test/breed_service_test.dart
```

The tests verify:
- Species and breed loading functionality
- Species ID mapping accuracy
- Cache behavior for both data types
- Error handling and fallbacks
- Data integrity and structure

## Performance

- **First Load**: Fetches both APIs sequentially (~2-3 seconds total)
- **Subsequent Loads**: Instant from cache for both data types
- **Cache Size**: Minimal (JSON strings in SharedPreferences)
- **Memory Usage**: Efficient with singleton pattern
- **Data Volume**: 112 species + 232 breeds loaded and cached

## Results

From the console logs, the integration successfully:
- ✅ Fetched **112 species** from the Species API
- ✅ Fetched **232 breeds** from the Breeds API
- ✅ Built species ID-to-name mapping for proper categorization
- ✅ Updated PetData with comprehensive species and breed information
- ✅ Cached both data types with 24-hour validity
- ✅ Provided detailed console logging for debugging
