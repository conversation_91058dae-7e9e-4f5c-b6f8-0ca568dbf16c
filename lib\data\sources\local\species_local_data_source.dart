import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../domain/entities/species.dart';

abstract class SpeciesLocalDataSource {
  Future<List<Species>> getCachedSpecies();
  Future<void> cacheSpecies(List<Species> species);
  Future<bool> hasCachedSpecies();
  Future<void> clearCache();
}

class SpeciesLocalDataSourceImpl implements SpeciesLocalDataSource {
  static const String _speciesKey = 'cached_species';
  static const String _cacheTimestampKey = 'species_cache_timestamp';
  static const int _cacheValidityHours = 24; // Cache valid for 24 hours

  @override
  Future<List<Species>> getCachedSpecies() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final speciesJson = prefs.getString(_speciesKey);
      
      if (speciesJson == null) {
        print('📱 No cached species found');
        return [];
      }

      // Check if cache is still valid
      final cacheTimestamp = prefs.getInt(_cacheTimestampKey) ?? 0;
      final now = DateTime.now().millisecondsSinceEpoch;
      final cacheAge = Duration(milliseconds: now - cacheTimestamp);
      
      if (cacheAge.inHours > _cacheValidityHours) {
        print('⏰ Species cache expired (${cacheAge.inHours} hours old), clearing...');
        await clearCache();
        return [];
      }

      final List<dynamic> jsonList = jsonDecode(speciesJson);
      final species = jsonList.map((json) => Species.fromJson(json as Map<String, dynamic>)).toList();
      
      print('📱 Loaded ${species.length} species from cache (${cacheAge.inHours}h old)');
      return species;
    } catch (e) {
      print('❌ Error loading cached species: $e');
      await clearCache(); // Clear corrupted cache
      return [];
    }
  }

  @override
  Future<void> cacheSpecies(List<Species> species) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final speciesJson = jsonEncode(species.map((s) => s.toJson()).toList());
      
      await prefs.setString(_speciesKey, speciesJson);
      await prefs.setInt(_cacheTimestampKey, DateTime.now().millisecondsSinceEpoch);
      
      print('💾 Cached ${species.length} species successfully');
    } catch (e) {
      print('❌ Error caching species: $e');
      rethrow;
    }
  }

  @override
  Future<bool> hasCachedSpecies() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final speciesJson = prefs.getString(_speciesKey);
      
      if (speciesJson == null) return false;

      // Check if cache is still valid
      final cacheTimestamp = prefs.getInt(_cacheTimestampKey) ?? 0;
      final now = DateTime.now().millisecondsSinceEpoch;
      final cacheAge = Duration(milliseconds: now - cacheTimestamp);
      
      return cacheAge.inHours <= _cacheValidityHours;
    } catch (e) {
      print('❌ Error checking cached species: $e');
      return false;
    }
  }

  @override
  Future<void> clearCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_speciesKey);
      await prefs.remove(_cacheTimestampKey);
      print('🗑️ Species cache cleared');
    } catch (e) {
      print('❌ Error clearing species cache: $e');
    }
  }
}
