import 'package:flutter/material.dart';
import '../../controllers/theme_controller.dart';
import '../../controllers/home/<USER>';
import '../../controllers/home/<USER>';
import '../../widgets/home/<USER>';
import '../../widgets/home/<USER>';
import '../../widgets/home/<USER>';

class HomeView extends StatefulWidget {
  final ThemeController themeController;
  final VoidCallback? onLogout;
  const HomeView({super.key, required this.themeController, this.onLogout});

  @override
  State<HomeView> createState() => _HomeViewState();
}

class _HomeViewState extends State<HomeView> {
  final HomeController _homeController = HomeController();
  final NavigationController _navigationController = NavigationController();

  Future<void> _showLogoutDialog() async {
    await _homeController.showLogoutDialog(context, widget.onLogout);
  }

  @override
  Widget build(BuildContext context) {
    // ignore: deprecated_member_use
    return WillPopScope(
      onWillPop: () async => false,
      child: ListenableBuilder(
        listenable: widget.themeController,
        builder: (context, _) {
          return Scaffold(
            appBar: CustomAppBar(
              navigationController: _navigationController,
              themeController: widget.themeController,
            ),
            body: Column(
              children: [
                Expanded(
                  child: ContentViewManager(
                    navigationController: _navigationController,
                    themeController: widget.themeController,
                    onLogout: _showLogoutDialog,
                    homeController: _homeController,
                  ),
                ),
                CustomNavigationBar(controller: _navigationController),
              ],
            ),
          );
        },
      ),
    );
  }
}
