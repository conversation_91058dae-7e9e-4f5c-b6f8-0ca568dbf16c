import '../../domain/entities/breed.dart';
import '../../domain/repositories/breed_repository.dart';
import '../sources/local/breed_local_data_source.dart';
import '../sources/remote/breed_remote_data_source.dart';

class BreedRepositoryImpl implements BreedRepository {
  final BreedRemoteDataSource remoteDataSource;
  final BreedLocalDataSource localDataSource;

  BreedRepositoryImpl({
    required this.remoteDataSource,
    required this.localDataSource,
  });

  @override
  Future<List<Breed>> getBreeds() async {
    return await remoteDataSource.getBreeds();
  }

  @override
  Future<void> cacheBreeds(List<Breed> breeds) async {
    await localDataSource.cacheBreeds(breeds);
  }

  @override
  Future<List<Breed>> getCachedBreeds() async {
    return await localDataSource.getCachedBreeds();
  }

  @override
  Future<bool> hasCachedBreeds() async {
    return await localDataSource.hasCachedBreeds();
  }
}
