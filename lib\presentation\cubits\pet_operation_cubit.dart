import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import '../../core/di/dependency_injection.dart';
import '../../domain/entities/update_pet_request.dart';
import '../../domain/entities/create_pet_request.dart';
import '../../services/pet_service.dart';
import '../controllers/home/<USER>';
import 'pet_cache_cubit.dart';
import 'pet_list_cubit.dart';

// States
abstract class PetOperationState extends Equatable {
  const PetOperationState();

  @override
  List<Object?> get props => [];
}

class PetOperationInitial extends PetOperationState {}

class PetOperationLoading extends PetOperationState {}

class PetOperationSuccess extends PetOperationState {
  final String message;
  final String operationType; // 'create', 'update', 'delete'

  const PetOperationSuccess({
    required this.message,
    required this.operationType,
  });

  @override
  List<Object?> get props => [message, operationType];
}

class PetOperationError extends PetOperationState {
  final String message;

  const PetOperationError(this.message);

  @override
  List<Object?> get props => [message];
}

class PetOperationCubit extends Cubit<PetOperationState> {
  final PetService _petService;
  final NavigationController? _navigationController;
  final PetCacheCubit? _petCacheCubit;
  final PetListCubit? _petListCubit;

  PetOperationCubit({
    PetService? petService,
    NavigationController? navigationController,
    PetCacheCubit? petCacheCubit,
    PetListCubit? petListCubit,
  })  : _petService = petService ?? getIt<PetService>(),
        _navigationController = navigationController,
        _petCacheCubit = petCacheCubit,
        _petListCubit = petListCubit,
        super(PetOperationInitial());

  Future<void> updatePet(UpdatePetRequest request) async {
    emit(PetOperationLoading());

    try {
      print('🔄 Starting pet update operation for: ${request.petName}');
      
      // Perform the update
      final response = await _petService.updatePet(request);
      
      if (response.success) {
        print('✅ Pet updated successfully: ${request.petName}');
        
        // Navigate to pets tab first
        _navigationController?.setTab(NavigationTab.pets);
        
        // Clear cache to force fresh data
        await _petCacheCubit?.clearCache();
        
        // Send request to get all pets with fresh data
        await _petListCubit?.loadPets(forceRefresh: true);
        
        emit(PetOperationSuccess(
          message: 'Pet "${request.petName}" updated successfully!',
          operationType: 'update',
        ));
      } else {
        final errorMessage = response.message ?? 'Failed to update pet';
        print('❌ Pet update failed: $errorMessage');
        emit(PetOperationError(errorMessage));
      }
    } catch (e) {
      final errorMessage = 'Failed to update pet: ${e.toString()}';
      print('❌ Pet update error: $errorMessage');
      emit(PetOperationError(errorMessage));
    }
  }

  Future<void> createPet(CreatePetRequest request) async {
    emit(PetOperationLoading());

    try {
      print('🔄 Starting pet creation operation for: ${request.petName}');
      
      // Perform the creation
      final response = await _petService.createPet(request);
      
      if (response.success) {
        print('✅ Pet created successfully: ${request.petName}');
        
        // Navigate to pets tab first
        _navigationController?.setTab(NavigationTab.pets);
        
        // Clear cache to force fresh data
        await _petCacheCubit?.clearCache();
        
        // Send request to get all pets with fresh data
        await _petListCubit?.loadPets(forceRefresh: true);
        
        emit(PetOperationSuccess(
          message: 'Pet "${request.petName}" created successfully!',
          operationType: 'create',
        ));
      } else {
        final errorMessage = response.message ?? 'Failed to create pet';
        print('❌ Pet creation failed: $errorMessage');
        emit(PetOperationError(errorMessage));
      }
    } catch (e) {
      final errorMessage = 'Failed to create pet: ${e.toString()}';
      print('❌ Pet creation error: $errorMessage');
      emit(PetOperationError(errorMessage));
    }
  }

  void reset() {
    emit(PetOperationInitial());
  }
}
