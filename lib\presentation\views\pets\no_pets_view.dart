import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../controllers/theme_controller.dart';
import '../../cubits/pets_cubit.dart';
import 'pet_form_view.dart';


class NoPetsView extends StatelessWidget {
  final ThemeController themeController;

  const NoPetsView({Key? key, required this.themeController}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<PetsCubit, PetsState>(
      builder: (context, state) {
        return Scaffold(
          body: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                ClipOval(
                  child: Container(
                    width: 200,
                    height: 200,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      image: DecorationImage(
                        image: AssetImage('assets/no_pets.jpg'),
                        fit: BoxFit.cover,
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 20),
                const Text(
                  'No Pets Found',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 10),
                const Text(
                  'Add your first pet to get started',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.grey,
                  ),
                ),
              ],
            ),
          ),
          floatingActionButton: state is PetsLoaded && state.pets.isEmpty
              ? FloatingActionButton.extended(
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (_) => PetFormView(
                          themeController: themeController,
                        ),
                      ),
                    );
                  },
                  label: const Text('Add Pet'),
                  icon: const Icon(Icons.add),
                )
              : null,
        );
      },
    );
  }
}
