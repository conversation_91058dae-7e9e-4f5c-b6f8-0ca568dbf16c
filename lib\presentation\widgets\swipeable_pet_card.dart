import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../domain/entities/pet_response.dart';
import '../cubits/pet_actions_cubit.dart';
import 'pet_card.dart';

class SwipeablePetCard extends StatelessWidget {
  final PetResponse pet;
  final VoidCallback? onTap;
  final VoidCallback? onEditConfirmed;
  final bool isDeleting;

  const SwipeablePetCard({
    Key? key,
    required this.pet,
    this.onTap,
    this.onEditConfirmed,
    this.isDeleting = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocListener<PetActionsCubit, PetActionsState>(
      listener: (context, state) {
        if (state is PetActionsSuccess) {
          if (state.actionType == 'delete' && state.petId == pet.id) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: Colors.green,
                duration: const Duration(seconds: 2),
              ),
            );
          } else if (state.actionType == 'edit' && state.petId == pet.id) {
            // Trigger the edit navigation callback
            onEditConfirmed?.call();
          }
        } else if (state is PetActionsError) {
          if (state.petId == pet.id) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: Colors.red,
                duration: const Duration(seconds: 3),
              ),
            );
          }
        }
      },
      child: Dismissible(
        key: Key('pet_${pet.id}'),
        background: _buildDeleteBackground(),
        secondaryBackground: _buildEditBackground(),
        confirmDismiss: (direction) async {
          if (direction == DismissDirection.startToEnd) {
            // Swipe right to left - Delete
            return await _showDeleteConfirmDialog(context);
          } else if (direction == DismissDirection.endToStart) {
            // Swipe left to right - Edit
            return await _showEditConfirmDialog(context);
          }
          return false;
        },
        onDismissed: (direction) {
          if (direction == DismissDirection.startToEnd) {
            // Delete action
            context.read<PetActionsCubit>().deletePet(pet);
          } else if (direction == DismissDirection.endToStart) {
            // Edit action
            context.read<PetActionsCubit>().navigateToEditPet(pet);
          }
        },
        child: BlocBuilder<PetActionsCubit, PetActionsState>(
          builder: (context, state) {
            final isCurrentlyDeleting = state is PetActionsLoading &&
                state.petId == pet.id &&
                state.actionType == 'delete';

            return PetCard(
              pet: pet,
              onDelete: () {
                // This is kept for backward compatibility but won't be used
                // since we're using swipe gestures now
              },
              onTap: onTap,
              isDeleting: isCurrentlyDeleting || isDeleting,
            );
          },
        ),
      ),
    );
  }

  Widget _buildDeleteBackground() {
    return Container(
      alignment: Alignment.centerLeft,
      padding: const EdgeInsets.only(left: 20),
      decoration: BoxDecoration(
        color: Colors.red,
        borderRadius: BorderRadius.circular(12),
      ),
      child: const Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Icon(
            Icons.delete,
            color: Colors.white,
            size: 32,
          ),
          SizedBox(width: 8),
          Text(
            'DELETE',
            style: TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
              fontSize: 16,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEditBackground() {
    return Container(
      alignment: Alignment.centerRight,
      padding: const EdgeInsets.only(right: 20),
      decoration: BoxDecoration(
        color: Colors.blue,
        borderRadius: BorderRadius.circular(12),
      ),
      child: const Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          Text(
            'EDIT',
            style: TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
              fontSize: 16,
            ),
          ),
          SizedBox(width: 8),
          Icon(
            Icons.edit,
            color: Colors.white,
            size: 32,
          ),
        ],
      ),
    );
  }

  Future<bool> _showDeleteConfirmDialog(BuildContext context) async {
    return await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Row(
            children: [
              Icon(Icons.warning, color: Colors.red),
              SizedBox(width: 8),
              Text('Confirm Delete'),
            ],
          ),
          content: Text(
            'Are you sure you want to delete "${pet.petName}"?\n\nThis action cannot be undone.',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
              ),
              child: const Text('Delete'),
            ),
          ],
        );
      },
    ) ?? false;
  }

  Future<bool> _showEditConfirmDialog(BuildContext context) async {
    return await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Row(
            children: [
              Icon(Icons.edit, color: Colors.blue),
              SizedBox(width: 8),
              Text('Edit Pet'),
            ],
          ),
          content: Text(
            'Do you want to edit "${pet.petName}"?',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
              ),
              child: const Text('Edit'),
            ),
          ],
        );
      },
    ) ?? false;
  }
}
