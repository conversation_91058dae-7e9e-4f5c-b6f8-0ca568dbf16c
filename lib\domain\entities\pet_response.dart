class PetOwner {
  final String fullName;
  final String? address;
  final String? imageName;
  final String? image;
  final DateTime? birthDate;

  const PetOwner({
    required this.fullName,
    this.address,
    this.imageName,
    this.image,
    this.birthDate,
  });

  factory PetOwner.fromJson(Map<String, dynamic> json) {
    return PetOwner(
      fullName: json['fullName'] ?? '',
      address: json['address'],
      imageName: json['imageName'],
      image: json['image'],
      birthDate: json['birthDate'] != null ? DateTime.parse(json['birthDate']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'fullName': fullName,
      'address': address,
      'imageName': imageName,
      'image': image,
      'birthDate': birthDate?.toIso8601String(),
    };
  }
}

class PetBreed {
  final String enBreed;
  final String arBreed;

  const PetBreed({
    required this.enBreed,
    required this.arBreed,
  });

  factory PetBreed.fromJson(Map<String, dynamic> json) {
    return PetBreed(
      enBreed: json['enBreed'] ?? '',
      arBreed: json['arBreed'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'enBreed': enBreed,
      'arBreed': arBreed,
    };
  }
}

class PetSpecieType {
  final String arType;
  final String enType;

  const PetSpecieType({
    required this.arType,
    required this.enType,
  });

  factory PetSpecieType.fromJson(Map<String, dynamic> json) {
    return PetSpecieType(
      arType: json['arType'] ?? '',
      enType: json['enType'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'arType': arType,
      'enType': enType,
    };
  }
}

class PetResponse {
  final String id;
  final String petName;
  final int gender;
  final String? breedId;
  final String? imageName;
  final DateTime birthdate;
  final String ownerId;
  final String specieId;
  final bool isSpayed;
  final PetOwner owner;
  final PetBreed breed;
  final PetSpecieType specie;
  final String? passportImage;
  final String? passportnumber;
  final String? microShipNumber;
  final String? qrCode;
  final String? qrCodeId;

  const PetResponse({
    required this.id,
    required this.petName,
    required this.gender,
    this.breedId,
    this.imageName,
    required this.birthdate,
    required this.ownerId,
    required this.specieId,
    required this.isSpayed,
    required this.owner,
    required this.breed,
    required this.specie,
    this.passportImage,
    this.passportnumber,
    this.microShipNumber,
    this.qrCode,
    this.qrCodeId,
  });

  factory PetResponse.fromJson(Map<String, dynamic> json) {
    try {
      return PetResponse(
        id: json['id'] ?? '',
        petName: json['petName'] ?? '',
        gender: json['gender'] ?? 0,
        breedId: json['breedId'] ?? '',
        imageName: json['imageName'],
        birthdate: json['birthdate'] != null
            ? DateTime.parse(json['birthdate'])
            : DateTime.now(),
        ownerId: json['ownerId'] ?? '',
        specieId: json['specieId'] ?? '',
        isSpayed: json['isSpayed'] ?? false,
        owner: PetOwner.fromJson(json['owner'] ?? {}),
        breed: json['breed'] != null
            ? PetBreed.fromJson(json['breed'])
            : const PetBreed(enBreed: 'Unknown', arBreed: 'غير معروف'),
        specie: PetSpecieType.fromJson(json['specie'] ?? {}),
        passportImage: json['passportImage'],
        passportnumber: json['passportnumber'],
        microShipNumber: json['microShipNumber'],
        qrCode: json['qrCode'],
        qrCodeId: json['qrCodeId'],
      );
    } catch (e) {
      print('❌ Error parsing PetResponse: $e');
      print('🔍 JSON data: $json');
      rethrow;
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'petName': petName,
      'gender': gender,
      'breedId': breedId,
      'imageName': imageName,
      'birthdate': birthdate.toIso8601String(),
      'ownerId': ownerId,
      'specieId': specieId,
      'isSpayed': isSpayed,
      'owner': owner.toJson(),
      'breed': breed.toJson(),
      'specie': specie.toJson(),
      'passportImage': passportImage,
      'passportnumber': passportnumber,
      'microShipNumber': microShipNumber,
      'qrCode': qrCode,
      'qrCodeId': qrCodeId,
    };
  }

  String get genderText => gender == 0 ? 'Male' : 'Female';

  @override
  String toString() => 'PetResponse(id: $id, petName: $petName, breed: ${breed.enBreed})';
}
