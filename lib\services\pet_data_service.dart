import '../domain/entities/breed.dart';
import '../domain/entities/species.dart';
import '../domain/entities/pet_data.dart';
import '../domain/usecases/get_breeds_usecase.dart';
import '../domain/usecases/get_species_usecase.dart';

class PetDataService {
  final GetBreedsUseCase _getBreedsUseCase;
  final GetSpeciesUseCase _getSpeciesUseCase;

  Map<String, String> _speciesIdToNameMap = {};
  Map<String, String> _speciesNameToIdMap = {};
  List<Breed> _cachedBreeds = [];

  PetDataService({
    required GetBreedsUseCase getBreedsUseCase,
    required GetSpeciesUseCase getSpeciesUseCase,
  })  : _getBreedsUseCase = getBreedsUseCase,
        _getSpeciesUseCase = getSpeciesUseCase;

  Future<void> loadAllPetData() async {
    try {
      print('🐾 Starting pet data loading process...');
      
      // Load species first to build the mapping
      final species = await _getSpeciesUseCase();
      _buildSpeciesMapping(species);

      // Then load breeds with proper species mapping
      final breeds = await _getBreedsUseCase();
      _cachedBreeds = breeds;
      
      if (breeds.isNotEmpty) {
        _updatePetDataWithBreedsAndSpecies(breeds, species);
        print('✅ Successfully updated PetData with ${breeds.length} breeds and ${species.length} species');
      } else {
        print('⚠️ No breeds received from API');
      }
    } catch (e) {
      print('❌ Failed to load pet data: $e');
      // Don't throw - let the app continue with default data
    }
  }

  void _buildSpeciesMapping(List<Species> species) {
    _speciesIdToNameMap.clear();
    _speciesNameToIdMap.clear();
    for (final s in species) {
      _speciesIdToNameMap[s.id] = s.enName;
      _speciesNameToIdMap[s.enName] = s.id;
    }
    print('📋 Built species mapping for ${species.length} species');
  }

  void _updatePetDataWithBreedsAndSpecies(List<Breed> breeds, List<Species> species) {
    // Group breeds by species using the mapping
    final Map<String, List<String>> speciesBreedMap = {};
    
    for (final breed in breeds) {
      // Use the species mapping to get proper species names
      String speciesName = breed.species;
      if (_speciesIdToNameMap.containsValue(breed.species)) {
        speciesName = breed.species;
      } else {
        // Try to find species name by ID if breed.species is actually an ID
        speciesName = _speciesIdToNameMap[breed.species] ?? breed.species;
      }
      
      if (!speciesBreedMap.containsKey(speciesName)) {
        speciesBreedMap[speciesName] = [];
      }
      speciesBreedMap[speciesName]!.add(breed.name);
    }

    // Add species that don't have breeds yet
    for (final s in species) {
      if (!speciesBreedMap.containsKey(s.enName)) {
        speciesBreedMap[s.enName] = [];
      }
    }

    // Convert to PetSpecies list
    final List<PetSpecies> newSpecies = speciesBreedMap.entries.map((entry) {
      final breedList = entry.value;
      // Add "Other" option if not already present
      if (!breedList.contains('Other')) {
        breedList.add('Other');
      }
      
      return PetSpecies(
        name: entry.key,
        breeds: breedList,
      );
    }).toList();

    // Update PetData
    if (newSpecies.isNotEmpty) {
      PetData.updateSpecies(newSpecies);
      
      // Log the updated data
      print('📊 Updated pet data:');
      for (final species in newSpecies) {
        print('  ${species.name}: ${species.breeds.length} breeds');
      }
    }
  }

  String getSpeciesNameById(String speciesId) {
    return _speciesIdToNameMap[speciesId] ?? 'Other';
  }

  String? getSpeciesIdByName(String speciesName) {
    return _speciesNameToIdMap[speciesName];
  }

  String? getBreedIdByName(String breedName) {
    for (final breed in _cachedBreeds) {
      if (breed.name == breedName) {
        return breed.id; // Return string ID directly
      }
    }
    return null;
  }

  List<Breed> getBreedsBySpeciesName(String speciesName) {
    final speciesId = getSpeciesIdByName(speciesName);
    if (speciesId == null) return [];

    return _cachedBreeds.where((breed) => breed.species == speciesId).toList();
  }

  Future<List<Breed>> getBreeds() async {
    return await _getBreedsUseCase();
  }

  Future<List<Species>> getSpecies() async {
    return await _getSpeciesUseCase();
  }

  List<String> getAvailableSpecies() {
    return _speciesNameToIdMap.keys.toList();
  }
}
