import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import '../../core/di/dependency_injection.dart';
import '../../domain/entities/pet_response.dart';
import '../../domain/entities/update_pet_request.dart';
import '../../domain/entities/update_pet_response.dart';
import '../../services/pet_data_service.dart';

// States
abstract class EditPetState extends Equatable {
  @override
  List<Object?> get props => [];
}

class EditPetInitial extends EditPetState {}

class EditPetLoading extends EditPetState {}

class EditPetFormState extends EditPetState {
  final String petName;
  final String selectedGender;
  final String selectedSpecies;
  final String selectedBreed;
  final DateTime selectedDate;
  final bool isSpayed;
  final String passportNumber;
  final String microchipNumber;
  final List<String> availableSpecies;
  final List<String> availableBreeds;
  final bool isFormValid;

  EditPetFormState({
    required this.petName,
    required this.selectedGender,
    required this.selectedSpecies,
    required this.selectedBreed,
    required this.selectedDate,
    required this.isSpayed,
    required this.passportNumber,
    required this.microchipNumber,
    required this.availableSpecies,
    required this.availableBreeds,
    required this.isFormValid,
  });

  EditPetFormState copyWith({
    String? petName,
    String? selectedGender,
    String? selectedSpecies,
    String? selectedBreed,
    DateTime? selectedDate,
    bool? isSpayed,
    String? passportNumber,
    String? microchipNumber,
    List<String>? availableSpecies,
    List<String>? availableBreeds,
    bool? isFormValid,
  }) {
    return EditPetFormState(
      petName: petName ?? this.petName,
      selectedGender: selectedGender ?? this.selectedGender,
      selectedSpecies: selectedSpecies ?? this.selectedSpecies,
      selectedBreed: selectedBreed ?? this.selectedBreed,
      selectedDate: selectedDate ?? this.selectedDate,
      isSpayed: isSpayed ?? this.isSpayed,
      passportNumber: passportNumber ?? this.passportNumber,
      microchipNumber: microchipNumber ?? this.microchipNumber,
      availableSpecies: availableSpecies ?? this.availableSpecies,
      availableBreeds: availableBreeds ?? this.availableBreeds,
      isFormValid: isFormValid ?? this.isFormValid,
    );
  }

  @override
  List<Object?> get props => [
        petName,
        selectedGender,
        selectedSpecies,
        selectedBreed,
        selectedDate,
        isSpayed,
        passportNumber,
        microchipNumber,
        availableSpecies,
        availableBreeds,
        isFormValid,
      ];
}

class EditPetSuccess extends EditPetState {
  final UpdatePetResponse response;

  EditPetSuccess(this.response);

  @override
  List<Object?> get props => [response];
}

class EditPetError extends EditPetState {
  final String message;

  EditPetError(this.message);

  @override
  List<Object?> get props => [message];
}

// Cubit
class EditPetCubit extends Cubit<EditPetState> {
  final PetDataService _petDataService;

  EditPetCubit({
    PetDataService? petDataService,
  })  : _petDataService = petDataService ?? getIt<PetDataService>(),
        super(EditPetInitial());

  Future<void> initializeWithPet(PetResponse pet) async {
    emit(EditPetLoading());
    
    try {
      // Load pet data if not already loaded
      await _petDataService.loadAllPetData();
      
      final availableSpecies = _petDataService.getAvailableSpecies();
      final speciesName = _petDataService.getSpeciesNameById(pet.specieId);
      final availableBreeds = _petDataService.getBreedsBySpeciesName(speciesName)
          .map((breed) => breed.name)
          .toList();

      final initialState = EditPetFormState(
        petName: pet.petName,
        selectedGender: pet.genderText,
        selectedSpecies: speciesName,
        selectedBreed: pet.breed.enBreed,
        selectedDate: pet.birthdate,
        isSpayed: pet.isSpayed,
        passportNumber: pet.passportnumber ?? '',
        microchipNumber: pet.microShipNumber ?? '',
        availableSpecies: availableSpecies,
        availableBreeds: availableBreeds,
        isFormValid: true,
      );

      emit(initialState);
    } catch (e) {
      emit(EditPetError('Failed to initialize pet data: ${e.toString()}'));
    }
  }

  void updatePetName(String name) {
    final currentState = state;
    if (currentState is EditPetFormState) {
      emit(currentState.copyWith(
        petName: name,
        isFormValid: _validateForm(currentState.copyWith(petName: name)),
      ));
    }
  }

  void updateGender(String gender) {
    final currentState = state;
    if (currentState is EditPetFormState) {
      emit(currentState.copyWith(
        selectedGender: gender,
        isFormValid: _validateForm(currentState.copyWith(selectedGender: gender)),
      ));
    }
  }

  void updateSpecies(String species) {
    final currentState = state;
    if (currentState is EditPetFormState) {
      final availableBreeds = _petDataService.getBreedsBySpeciesName(species)
          .map((breed) => breed.name)
          .toList();
      
      final firstBreed = availableBreeds.isNotEmpty ? availableBreeds.first : '';
      
      emit(currentState.copyWith(
        selectedSpecies: species,
        selectedBreed: firstBreed,
        availableBreeds: availableBreeds,
        isFormValid: _validateForm(currentState.copyWith(
          selectedSpecies: species,
          selectedBreed: firstBreed,
        )),
      ));
    }
  }

  void updateBreed(String breed) {
    final currentState = state;
    if (currentState is EditPetFormState) {
      emit(currentState.copyWith(
        selectedBreed: breed,
        isFormValid: _validateForm(currentState.copyWith(selectedBreed: breed)),
      ));
    }
  }

  void updateBirthdate(DateTime date) {
    final currentState = state;
    if (currentState is EditPetFormState) {
      emit(currentState.copyWith(
        selectedDate: date,
        isFormValid: _validateForm(currentState.copyWith(selectedDate: date)),
      ));
    }
  }

  void updateSpayedStatus(bool isSpayed) {
    final currentState = state;
    if (currentState is EditPetFormState) {
      emit(currentState.copyWith(
        isSpayed: isSpayed,
        isFormValid: _validateForm(currentState.copyWith(isSpayed: isSpayed)),
      ));
    }
  }

  void updatePassportNumber(String passportNumber) {
    final currentState = state;
    if (currentState is EditPetFormState) {
      emit(currentState.copyWith(
        passportNumber: passportNumber,
        isFormValid: _validateForm(currentState.copyWith(passportNumber: passportNumber)),
      ));
    }
  }

  void updateMicrochipNumber(String microchipNumber) {
    final currentState = state;
    if (currentState is EditPetFormState) {
      emit(currentState.copyWith(
        microchipNumber: microchipNumber,
        isFormValid: _validateForm(currentState.copyWith(microchipNumber: microchipNumber)),
      ));
    }
  }

  bool _validateForm(EditPetFormState formState) {
    return formState.petName.trim().isNotEmpty &&
           formState.selectedGender.isNotEmpty &&
           formState.selectedSpecies.isNotEmpty &&
           formState.selectedBreed.isNotEmpty;
  }

  UpdatePetRequest? buildUpdateRequest(String petId) {
    final currentState = state;
    if (currentState is! EditPetFormState || !currentState.isFormValid) {
      return null;
    }

    try {
      final formState = currentState;

      // Get species and breed IDs
      final speciesId = _petDataService.getSpeciesIdByName(formState.selectedSpecies);
      final breedId = _petDataService.getBreedIdByName(formState.selectedBreed);

      if (speciesId == null || breedId == null) {
        emit(EditPetError('Invalid species or breed selection'));
        return null;
      }

      // Convert gender string to int (0 = Male, 1 = Female)
      final genderInt = formState.selectedGender.toLowerCase() == 'male' ? 0 : 1;

      // Create the update request
      return UpdatePetRequest(
        id: petId,
        petName: formState.petName.trim(),
        gender: genderInt,
        breedId: breedId,
        specieId: speciesId,
        birthdate: formState.selectedDate,
        isSpayed: formState.isSpayed,
        passportnumber: formState.passportNumber.trim().isEmpty ? null : formState.passportNumber.trim(),
        microShipNumber: formState.microchipNumber.trim().isEmpty ? null : formState.microchipNumber.trim(),
      );
    } catch (e) {
      emit(EditPetError('Failed to build update request: ${e.toString()}'));
      return null;
    }
  }
}
