import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../controllers/home/<USER>';
import '../../controllers/home/<USER>';
import '../../controllers/theme_controller.dart';
import '../../views/settings/settings_view.dart';
import '../../views/home/<USER>';
import '../../views/pets/pets_list_view.dart';
import '../../cubits/pet_list_cubit.dart';
import '../../cubits/pet_cache_cubit.dart';

class ContentViewManager extends StatelessWidget {
  final NavigationController navigationController;
  final ThemeController themeController;
  final VoidCallback onLogout;
  final HomeController homeController;
  
  const ContentViewManager({
    Key? key,
    required this.navigationController,
    required this.themeController,
    required this.onLogout,
    required this.homeController,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ListenableBuilder(
      listenable: navigationController,
      builder: (context, _) {
        return _buildCurrentView(context);
      },
    );
  }

  Widget _buildCurrentView(BuildContext context) {
    switch (navigationController.currentTab) {
      case NavigationTab.settings:
        return SettingsView(
          themeController: themeController,
          onLogout: onLogout,
        );
      case NavigationTab.history:
        return const Center(child: Text('History View - Coming Soon'));
      case NavigationTab.pets:
        return MultiBlocProvider(
          providers: [
            BlocProvider(create: (context) => PetCacheCubit()),
            BlocProvider(
              create: (context) => PetListCubit(context.read<PetCacheCubit>())..loadPets(),
            ),
          ],
          child: PetsListView(navigationController: navigationController),
        );
      case NavigationTab.addUser:
        return const Center(child: Text('Add User View - Coming Soon'));
      case NavigationTab.home:
        return HomeContentView(
          isDark: themeController.isDark,
          homeController: homeController,
        );
    }
  }



}