class PetSpecies {
  final String name;
  final List<String> breeds;

  const PetSpecies({
    required this.name,
    required this.breeds,
  });

  factory PetSpecies.fromJson(Map<String, dynamic> json) {
    return PetSpecies(
      name: json['name'] ?? '',
      breeds: List<String>.from(json['breeds'] ?? []),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'breeds': breeds,
    };
  }
}

class PetData {
  static List<PetSpecies> _species = [
    const PetSpecies(
      name: 'Dog',
      breeds: [
        'Labrador Retriever',
        'German Shepherd',
        'Golden Retriever',
        'French Bulldog',
        'Dalmatian',
        'Husky',
        'Poodle',
        'Rottweiler',
        'Other',
      ],
    ),
    const PetSpecies(
      name: '<PERSON>',
      breeds: [
        'Persian',
        'Maine Coon',
        'Siamese',
        'British Shorthair',
        'Bengal',
        'Ragdoll',
        'Scottish Fold',
        'Other',
      ],
    ),
  ];

  static List<PetSpecies> get species => _species;

  static void updateSpecies(List<PetSpecies> newSpecies) {
    _species = newSpecies;
  }
}
