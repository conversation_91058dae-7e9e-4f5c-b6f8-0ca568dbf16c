import '../repositories/pet_repository.dart';

class DeletePetUseCase {
  final PetRepository repository;

  DeletePetUseCase(this.repository);

  Future<bool> call(String petId, String petName) async {
    try {
      print('🗑️ Deleting pet: $petName (ID: $petId)');

      final success = await repository.deletePet(petId);

      if (success) {
        print('✅ Pet deleted successfully: $petName');
        return true;
      } else {
        print('❌ Failed to delete pet: $petName');
        return false;
      }
    } catch (e) {
      print('❌ Error in DeletePetUseCase: $e');
      rethrow;
    }
  }
}
