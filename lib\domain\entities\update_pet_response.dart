import 'pet_response.dart';

class UpdatePetResponse {
  final bool success;
  final String? message;
  final Map<String, dynamic>? errors;
  final PetResponse? pet;

  const UpdatePetResponse({
    required this.success,
    this.message,
    this.errors,
    this.pet,
  });

  factory UpdatePetResponse.fromJson(Map<String, dynamic> json) {
    return UpdatePetResponse(
      success: json['success'] ?? false,
      message: json['message'],
      errors: json['errors'],
      pet: json['data'] != null ? PetResponse.fromJson(json['data']) : null,
    );
  }

  @override
  String toString() => 'UpdatePetResponse(success: $success, message: $message)';
}
