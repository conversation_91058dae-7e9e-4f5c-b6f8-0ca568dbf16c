import '../entities/create_pet_request.dart';
import '../entities/create_pet_response.dart';
import '../entities/update_pet_request.dart';
import '../entities/update_pet_response.dart';
import '../entities/pet_response.dart';

abstract class PetRepository {
  Future<CreatePetResponse> createPet(CreatePetRequest request);
  Future<UpdatePetResponse> updatePet(UpdatePetRequest request);
  Future<List<PetResponse>> getAllPets();
  Future<bool> deletePet(String petId);
}
