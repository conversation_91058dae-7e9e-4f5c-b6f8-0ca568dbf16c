class Breed {
  final String id; // Changed to String to store original UUID
  final String name;
  final String species;

  const Breed({
    required this.id,
    required this.name,
    required this.species,
  });

  factory Breed.fromJson(Map<String, dynamic> json) {
    return Breed(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      species: json['species'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'species': species,
    };
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Breed &&
        other.id == id &&
        other.name == name &&
        other.species == species;
  }

  @override
  int get hashCode => id.hashCode ^ name.hashCode ^ species.hashCode;

  @override
  String toString() => 'Breed(id: $id, name: $name, species: $species)';
}
