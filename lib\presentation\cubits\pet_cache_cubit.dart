import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import '../../domain/entities/pet_response.dart';

// States
abstract class PetCacheState extends Equatable {
  const PetCacheState();

  @override
  List<Object> get props => [];
}

class PetCacheInitial extends PetCacheState {}

class PetCacheLoaded extends PetCacheState {
  final List<PetResponse> cachedPets;
  final DateTime lastUpdated;

  const PetCacheLoaded(this.cachedPets, this.lastUpdated);

  @override
  List<Object> get props => [cachedPets, lastUpdated];
}

class PetCacheError extends PetCacheState {
  final String message;

  const PetCacheError(this.message);

  @override
  List<Object> get props => [message];
}

// Cubit
class PetCacheCubit extends Cubit<PetCacheState> {
  static const String _cacheKey = 'cached_pets';
  static const String _lastUpdatedKey = 'pets_last_updated';
  static const Duration _cacheValidDuration = Duration(hours: 1);

  PetCacheCubit() : super(PetCacheInitial());

  Future<void> loadCachedPets() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cachedData = prefs.getString(_cacheKey);
      final lastUpdatedString = prefs.getString(_lastUpdatedKey);

      if (cachedData != null && lastUpdatedString != null) {
        final lastUpdated = DateTime.parse(lastUpdatedString);
        final List<dynamic> jsonList = json.decode(cachedData);
        final pets = jsonList.map((json) => PetResponse.fromJson(json)).toList();
        
        emit(PetCacheLoaded(pets, lastUpdated));
      } else {
        emit(PetCacheLoaded(const [], DateTime.now()));
      }
    } catch (e) {
      emit(PetCacheError('Failed to load cached pets: $e'));
    }
  }

  Future<void> cachePets(List<PetResponse> pets) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final now = DateTime.now();
      
      final jsonList = pets.map((pet) => pet.toJson()).toList();
      await prefs.setString(_cacheKey, json.encode(jsonList));
      await prefs.setString(_lastUpdatedKey, now.toIso8601String());
      
      emit(PetCacheLoaded(pets, now));
    } catch (e) {
      emit(PetCacheError('Failed to cache pets: $e'));
    }
  }

  Future<void> addPetToCache(PetResponse pet) async {
    if (state is PetCacheLoaded) {
      final currentState = state as PetCacheLoaded;
      final updatedPets = List<PetResponse>.from(currentState.cachedPets)..add(pet);
      await cachePets(updatedPets);
    }
  }

  Future<void> removePetFromCache(String petId) async {
    if (state is PetCacheLoaded) {
      final currentState = state as PetCacheLoaded;
      final updatedPets = currentState.cachedPets.where((pet) => pet.id != petId).toList();
      await cachePets(updatedPets);
    }
  }

  Future<void> clearCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_cacheKey);
      await prefs.remove(_lastUpdatedKey);
      emit(PetCacheLoaded(const [], DateTime.now()));
    } catch (e) {
      emit(PetCacheError('Failed to clear cache: $e'));
    }
  }

  bool isCacheValid() {
    if (state is PetCacheLoaded) {
      final currentState = state as PetCacheLoaded;
      final now = DateTime.now();
      return now.difference(currentState.lastUpdated) < _cacheValidDuration;
    }
    return false;
  }

  List<PetResponse> get cachedPets {
    if (state is PetCacheLoaded) {
      return (state as PetCacheLoaded).cachedPets;
    }
    return [];
  }
}
