import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../../../domain/entities/breed.dart';

abstract class BreedRemoteDataSource {
  Future<List<Breed>> getBreeds();
}

class BreedRemoteDataSourceImpl implements BreedRemoteDataSource {
  final http.Client client;
  static const String baseUrl = 'https://squeakapi.veticareapp.com:8001/v1/api';

  BreedRemoteDataSourceImpl({http.Client? client}) : client = client ?? http.Client();

  @override
  Future<List<Breed>> getBreeds() async {
    try {
      // Get token from shared preferences
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('token');
      
      if (token == null || token.isEmpty) {
        throw Exception('No authentication token found');
      }

      final url = Uri.parse('$baseUrl/breed');
      
      print('🌐 Making API request to: $url');
      print('🔑 Using token: ${token.substring(0, 20)}...');

      final response = await client.get(
        url,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      print('📡 API Response Status: ${response.statusCode}');
      print('📡 API Response Body: ${response.body}');

      if (response.statusCode == 200) {
        final dynamic jsonResponse = jsonDecode(response.body);

        // Handle the specific API response structure
        List<dynamic> breedsJson;
        if (jsonResponse is Map<String, dynamic>) {
          if (jsonResponse.containsKey('data') && jsonResponse['data'] is Map<String, dynamic>) {
            final data = jsonResponse['data'] as Map<String, dynamic>;
            if (data.containsKey('breedDto')) {
              breedsJson = data['breedDto'] as List<dynamic>;
            } else {
              throw Exception('No breedDto found in data');
            }
          } else if (jsonResponse.containsKey('data') && jsonResponse['data'] is List<dynamic>) {
            breedsJson = jsonResponse['data'] as List<dynamic>;
          } else {
            throw Exception('Unexpected response format: Map without proper data structure');
          }
        } else if (jsonResponse is List<dynamic>) {
          breedsJson = jsonResponse;
        } else {
          throw Exception('Unexpected response format: ${jsonResponse.runtimeType}');
        }

        final breeds = breedsJson.map((json) {
          final breedMap = json as Map<String, dynamic>;
          // Map the API response to our Breed entity
          return Breed(
            id: breedMap['id'] ?? '', // Store original string ID
            name: breedMap['enBreed'] ?? breedMap['name'] ?? '',
            species: breedMap['specieId'] ?? '', // Store the species ID, will be mapped later
          );
        }).toList();

        print('✅ Successfully parsed ${breeds.length} breeds');
        return breeds;
      } else {
        throw Exception('Failed to fetch breeds: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      print('❌ Error fetching breeds: $e');
      rethrow;
    }
  }


}
