import 'package:flutter_bloc/flutter_bloc.dart';
import '../../core/di/dependency_injection.dart';
import '../../domain/entities/pet_response.dart';
import '../../services/pet_service.dart';
import 'pet_cache_cubit.dart';

// States
abstract class PetListState {}

class PetListInitial extends PetListState {}

class PetListLoading extends PetListState {}

class PetListLoaded extends PetListState {
  final List<PetResponse> pets;

  PetListLoaded(this.pets);
}

class PetListError extends PetListState {
  final String message;

  PetListError(this.message);
}

class PetListDeleting extends PetListState {
  final List<PetResponse> pets;
  final String deletingPetId;

  PetListDeleting(this.pets, this.deletingPetId);
}

// Cubit
class PetListCubit extends Cubit<PetListState> {
  final PetService _petService;
  final PetCacheCubit _petCacheCubit;

  PetListCubit(
    this._petCacheCubit, {
    PetService? petService,
  })  : _petService = petService ?? getIt<PetService>(),
        super(PetListInitial()) {
    _petCacheCubit.loadCachedPets();
  }

  Future<void> loadPets({bool forceRefresh = false}) async {
    emit(PetListLoading());

    try {
      // Check cache first if not forcing refresh
      if (!forceRefresh && _petCacheCubit.isCacheValid()) {
        print('🐾 PetListCubit: Loading pets from cache...');
        final cachedPets = _petCacheCubit.cachedPets;
        emit(PetListLoaded(cachedPets));
        return;
      }

      print('🐾 PetListCubit: Loading pets from API...');
      final pets = await _petService.getAllPets();
      print('🐾 PetListCubit: Successfully loaded ${pets.length} pets');

      // Cache the pets
      await _petCacheCubit.cachePets(pets);

      emit(PetListLoaded(pets));
    } catch (e) {
      print('❌ PetListCubit: Error loading pets: $e');

      // Try to load from cache as fallback
      if (_petCacheCubit.cachedPets.isNotEmpty) {
        print('🐾 PetListCubit: Loading pets from cache as fallback...');
        emit(PetListLoaded(_petCacheCubit.cachedPets));
      } else {
        emit(PetListError('Failed to load pets: $e'));
      }
    }
  }

  Future<void> deletePet(String petId, String petName) async {
    final currentState = state;
    if (currentState is PetListLoaded) {
      emit(PetListDeleting(currentState.pets, petId));

      try {
        final success = await _petService.deletePet(petId, petName);

        if (success) {
          // Remove the deleted pet from the list and cache
          final updatedPets = currentState.pets.where((pet) => pet.id != petId).toList();
          await _petCacheCubit.removePetFromCache(petId);
          emit(PetListLoaded(updatedPets));
        } else {
          // Revert to the original state if deletion failed
          emit(PetListLoaded(currentState.pets));
          throw Exception('Failed to delete pet');
        }
      } catch (e) {
        // Revert to the original state on error
        emit(PetListLoaded(currentState.pets));
        emit(PetListError('Failed to delete pet: $e'));
      }
    }
  }

  Future<void> addPetToList(PetResponse pet) async {
    final currentState = state;
    if (currentState is PetListLoaded) {
      final updatedPets = List<PetResponse>.from(currentState.pets)..add(pet);
      await _petCacheCubit.addPetToCache(pet);
      emit(PetListLoaded(updatedPets));
    }
  }

  bool get hasPets {
    final currentState = state;
    return currentState is PetListLoaded && currentState.pets.isNotEmpty;
  }

  List<PetResponse> get pets {
    final currentState = state;
    if (currentState is PetListLoaded) {
      return currentState.pets;
    }
    return [];
  }
}
