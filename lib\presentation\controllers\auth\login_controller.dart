// ignore_for_file: use_build_context_synchronously
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../core/di/dependency_injection.dart';
import '../../../data/models/Auth/user_model.dart';
import '../../../services/Auth/auth_service.dart';
import '../../../services/pet_data_service.dart';

class LoginController {
  final AuthService _authService = AuthService();

  Future<bool> login(UserModel user, BuildContext context) async {
    if (!user.isValid) {
      _showSnackBar(context, 'Invalid credentials', isError: true);
      return false;
    }

    try {
      final response = await _authService.login(user.email, user.password);

      if (response.success && response.data != null) {
        // Store token and login state
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString('token', response.data!.token);
        await prefs.setBool('isLoggedIn', true);
        await prefs.setString('fullName', response.data!.fullName);
        await prefs.setString('phone', response.data!.phone);

        // Load pet data after successful login
        print('🔄 Loading pet data after successful login...');
        getIt<PetDataService>().loadAllPetData();

        _showSnackBar(context, 'Login successful: ${response.data!.email}');
        return true;
      } else {
        _showSnackBar(context, response.message.isNotEmpty ? response.message : 'Login failed', isError: true);
        return false;
      }
    } catch (e) {
      _showSnackBar(context, 'Error:  {e.toString()}', isError: true);
      return false;
    }
  }

  void forgotPassword(BuildContext context) {
    _showSnackBar(context, 'Forgot password clicked');
  }

  Future<void> logout(BuildContext context) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('token');
    await prefs.setBool('isLoggedIn', false);
    Navigator.pushReplacementNamed(context, '/');
  }

  void _showSnackBar(
    BuildContext context,
    String message, {
    bool isError = false,
  }) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isError
            ? Theme.of(context).colorScheme.error
            : Theme.of(context).colorScheme.primary,
      ),
    );
  }
}
