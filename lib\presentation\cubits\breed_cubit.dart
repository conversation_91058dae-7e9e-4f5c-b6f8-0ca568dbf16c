import 'package:flutter_bloc/flutter_bloc.dart';
import '../../core/di/dependency_injection.dart';
import '../../domain/entities/breed.dart';
import '../../domain/entities/pet_data.dart';
import '../../services/pet_data_service.dart';

abstract class BreedState {}

class BreedInitial extends BreedState {}

class BreedLoading extends BreedState {}

class BreedLoaded extends BreedState {
  final List<Breed> breeds;
  final List<PetSpecies> species;

  BreedLoaded({
    required this.breeds,
    required this.species,
  });
}

class BreedError extends BreedState {
  final String message;

  BreedError(this.message);
}

class BreedCubit extends Cubit<BreedState> {
  final PetDataService _petDataService;

  BreedCubit({PetDataService? petDataService})
      : _petDataService = petDataService ?? getIt<PetDataService>(),
        super(BreedInitial());

  Future<void> loadBreeds() async {
    emit(BreedLoading());
    try {
      await _petDataService.loadAllPetData();
      final breeds = await _petDataService.getBreeds();
      emit(BreedLoaded(
        breeds: breeds,
        species: PetData.species,
      ));
    } catch (e) {
      emit(BreedError(e.toString()));
      // Even on error, emit loaded state with default data
      emit(BreedLoaded(
        breeds: [],
        species: PetData.species,
      ));
    }
  }

  Future<void> refreshBreeds() async {
    await loadBreeds();
  }
}
