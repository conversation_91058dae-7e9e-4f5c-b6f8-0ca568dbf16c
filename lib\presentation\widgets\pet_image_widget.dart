import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../cubits/pet_image_cubit.dart';

class PetImageWidget extends StatelessWidget {
  final String? imageName;
  final double size;
  final bool isDark;

  const PetImageWidget({
    Key? key,
    required this.imageName,
    this.size = 60,
    required this.isDark,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => PetImageCubit()..loadImage(imageName),
      child: Container(
        width: size,
        height: size,
        decoration: BoxDecoration(
          color: isDark ? Colors.grey[700] : Colors.grey[200],
          shape: BoxShape.circle,
        ),
        child: BlocBuilder<PetImageCubit, PetImageState>(
          builder: (context, state) {
            if (state is PetImageLoading) {
              return Center(
                child: SizedBox(
                  width: size * 0.4,
                  height: size * 0.4,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    color: isDark ? Colors.white70 : Colors.black54,
                  ),
                ),
              );
            }

            if (state is PetImageLoaded) {
              return ClipOval(
                child: Image.network(
                  state.imageUrl,
                  width: size,
                  height: size,
                  fit: BoxFit.cover,
                  loadingBuilder: (context, child, loadingProgress) {
                    if (loadingProgress == null) return child;
                    
                    return Center(
                      child: SizedBox(
                        width: size * 0.4,
                        height: size * 0.4,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          value: loadingProgress.expectedTotalBytes != null
                              ? loadingProgress.cumulativeBytesLoaded /
                                  loadingProgress.expectedTotalBytes!
                              : null,
                          color: isDark ? Colors.white70 : Colors.black54,
                        ),
                      ),
                    );
                  },
                  errorBuilder: (context, error, stackTrace) {
                    // Notify cubit about the error
                    context.read<PetImageCubit>().onImageError();
                    return _buildFallbackIcon();
                  },
                ),
              );
            }

            // For PetImageError, PetImageEmpty, or PetImageInitial states
            return _buildFallbackIcon();
          },
        ),
      ),
    );
  }

  Widget _buildFallbackIcon() {
    return Icon(
      Icons.pets,
      size: size * 0.5,
      color: isDark ? Colors.grey[400] : Colors.grey[600],
    );
  }
}
