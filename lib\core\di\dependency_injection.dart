import 'package:get_it/get_it.dart';
import 'package:http/http.dart' as http;

// Data Sources
import '../../data/sources/remote/breed_remote_data_source.dart';
import '../../data/sources/remote/species_remote_data_source.dart';
import '../../data/sources/remote/pet_remote_data_source.dart';
import '../../data/sources/local/breed_local_data_source.dart';
import '../../data/sources/local/species_local_data_source.dart';

// Repositories
import '../../data/repos/breed_repository_impl.dart';
import '../../data/repos/species_repository_impl.dart';
import '../../data/repos/pet_repository_impl.dart';
import '../../domain/repositories/breed_repository.dart';
import '../../domain/repositories/species_repository.dart';
import '../../domain/repositories/pet_repository.dart';

// Use Cases
import '../../domain/usecases/get_breeds_usecase.dart';
import '../../domain/usecases/get_species_usecase.dart';
import '../../domain/usecases/create_pet_usecase.dart';
import '../../domain/usecases/update_pet_usecase.dart';
import '../../domain/usecases/get_all_pets_usecase.dart';
import '../../domain/usecases/delete_pet_usecase.dart';

// Services
import '../../services/breed_service.dart';
import '../../services/pet_service.dart';
import '../../services/pet_data_service.dart';

// Cubits
import '../../presentation/cubits/theme_cubit.dart';
import '../../presentation/cubits/pet_cache_cubit.dart';
import '../../presentation/cubits/pet_list_cubit.dart';
import '../../presentation/cubits/pet_operation_cubit.dart';
import '../../presentation/cubits/pet_actions_cubit.dart';
import '../../presentation/cubits/pet_actions_cubit.dart';

final GetIt getIt = GetIt.instance;

class DependencyInjection {
  static Future<void> init() async {
    // Register HTTP Client
    getIt.registerLazySingleton<http.Client>(() => http.Client());

    // Register Data Sources
    getIt.registerLazySingleton<BreedRemoteDataSource>(
      () => BreedRemoteDataSourceImpl(client: getIt()),
    );
    getIt.registerLazySingleton<BreedLocalDataSource>(
      () => BreedLocalDataSourceImpl(),
    );
    getIt.registerLazySingleton<SpeciesRemoteDataSource>(
      () => SpeciesRemoteDataSourceImpl(client: getIt()),
    );
    getIt.registerLazySingleton<SpeciesLocalDataSource>(
      () => SpeciesLocalDataSourceImpl(),
    );
    getIt.registerLazySingleton<PetRemoteDataSource>(
      () => PetRemoteDataSourceImpl(client: getIt()),
    );

    // Register Repositories
    getIt.registerLazySingleton<BreedRepository>(
      () => BreedRepositoryImpl(
        remoteDataSource: getIt(),
        localDataSource: getIt(),
      ),
    );
    getIt.registerLazySingleton<SpeciesRepository>(
      () => SpeciesRepositoryImpl(
        remoteDataSource: getIt(),
        localDataSource: getIt(),
      ),
    );
    getIt.registerLazySingleton<PetRepository>(
      () => PetRepositoryImpl(
        remoteDataSource: getIt(),
      ),
    );

    // Register Use Cases
    getIt.registerLazySingleton<GetBreedsUseCase>(
      () => GetBreedsUseCase(getIt()),
    );
    getIt.registerLazySingleton<GetSpeciesUseCase>(
      () => GetSpeciesUseCase(getIt()),
    );
    getIt.registerLazySingleton<CreatePetUseCase>(
      () => CreatePetUseCase(getIt()),
    );
    getIt.registerLazySingleton<UpdatePetUseCase>(
      () => UpdatePetUseCase(getIt()),
    );
    getIt.registerLazySingleton<GetAllPetsUseCase>(
      () => GetAllPetsUseCase(getIt()),
    );
    getIt.registerLazySingleton<DeletePetUseCase>(
      () => DeletePetUseCase(getIt()),
    );

    // Register Services
    getIt.registerLazySingleton<BreedService>(
      () => BreedService(getBreedsUseCase: getIt()),
    );
    getIt.registerLazySingleton<PetService>(
      () => PetService(
        createPetUseCase: getIt(),
        updatePetUseCase: getIt(),
        getAllPetsUseCase: getIt(),
        deletePetUseCase: getIt(),
      ),
    );
    getIt.registerLazySingleton<PetDataService>(
      () => PetDataService(
        getBreedsUseCase: getIt(),
        getSpeciesUseCase: getIt(),
      ),
    );

    // Register Cubits (as factories since they should be created fresh each time)
    getIt.registerFactory<ThemeCubit>(() => ThemeCubit());
    getIt.registerFactory<PetCacheCubit>(() => PetCacheCubit());
    getIt.registerFactory<PetListCubit>(() => PetListCubit(getIt<PetCacheCubit>()));
    getIt.registerFactory<PetOperationCubit>(() => PetOperationCubit());
    getIt.registerFactory<PetActionsCubit>(() => PetActionsCubit());
    getIt.registerFactory<PetActionsCubit>(() => PetActionsCubit());

    print('🔧 Dependency Injection initialized successfully');
  }

  static void reset() {
    getIt.reset();
  }
}
