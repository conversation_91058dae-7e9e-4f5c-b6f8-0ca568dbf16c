import '../entities/breed.dart';
import '../repositories/breed_repository.dart';

class GetBreedsUseCase {
  final BreedRepository repository;

  GetBreedsUseCase(this.repository);

  Future<List<Breed>> call() async {
    try {
      // First check if we have cached data
      if (await repository.hasCachedBreeds()) {
        print('🔄 Loading breeds from cache');
        return await repository.getCachedBreeds();
      }

      // If no cache, fetch from API
      print('🌐 Fetching breeds from API');
      final breeds = await repository.getBreeds();
      
      // Cache the results
      await repository.cacheBreeds(breeds);
      print('💾 Breeds cached successfully');
      
      return breeds;
    } catch (e) {
      print('❌ Error in GetBreedsUseCase: $e');
      
      // Try to return cached data as fallback
      if (await repository.hasCachedBreeds()) {
        print('🔄 Falling back to cached data');
        return await repository.getCachedBreeds();
      }
      
      rethrow;
    }
  }
}
