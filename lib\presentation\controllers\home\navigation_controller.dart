import 'package:flutter/material.dart';

enum NavigationTab {
  settings,
  history,
  pets,
  addUser,
  home,
}

class NavigationController extends ChangeNotifier {
  NavigationTab _currentTab = NavigationTab.home;
  
  NavigationTab get currentTab => _currentTab;
  
  void setTab(NavigationTab tab) {
    if (_currentTab != tab) {
      _currentTab = tab;
      notifyListeners();
    }
  }
  
  bool isSelected(NavigationTab tab) => _currentTab == tab;
} 