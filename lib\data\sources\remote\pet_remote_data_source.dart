import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../../../domain/entities/create_pet_request.dart';
import '../../../domain/entities/create_pet_response.dart';
import '../../../domain/entities/update_pet_request.dart';
import '../../../domain/entities/update_pet_response.dart';
import '../../../domain/entities/pet_response.dart';

abstract class PetRemoteDataSource {
  Future<CreatePetResponse> createPet(CreatePetRequest request);
  Future<UpdatePetResponse> updatePet(UpdatePetRequest request);
  Future<List<PetResponse>> getAllPets();
  Future<bool> deletePet(String petId);
}

class PetRemoteDataSourceImpl implements PetRemoteDataSource {
  final http.Client client;
  static const String baseUrl = 'https://squeakapi.veticareapp.com:8001/v1/api';

  PetRemoteDataSourceImpl({http.Client? client}) : client = client ?? http.Client();

  @override
  Future<CreatePetResponse> createPet(CreatePetRequest request) async {
    try {
      // Get token from shared preferences
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('token');
      
      if (token == null || token.isEmpty) {
        throw Exception('No authentication token found');
      }

      final url = Uri.parse('$baseUrl/pets');
      final requestBody = jsonEncode(request.toJson());
      
      print('🌐 Making Create Pet API request to: $url');
      print('🔑 Using token: ${token.substring(0, 20)}...');
      print('📤 Request body: $requestBody');

      final response = await client.post(
        url,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
        body: requestBody,
      );

      print('📡 Create Pet API Response Status: ${response.statusCode}');
      print('📡 Create Pet API Response Body: ${response.body}');

      if (response.statusCode == 200 || response.statusCode == 201) {
        final jsonResponse = jsonDecode(response.body) as Map<String, dynamic>;
        final createPetResponse = CreatePetResponse.fromJson(jsonResponse);
        
        print('✅ Pet creation API call successful');
        return createPetResponse;
      } else {
        // Handle error responses
        final errorResponse = response.body.isNotEmpty 
            ? jsonDecode(response.body) as Map<String, dynamic>
            : <String, dynamic>{};
            
        throw Exception('Failed to create pet: ${response.statusCode} - ${errorResponse['message'] ?? response.body}');
      }
    } catch (e) {
      print('❌ Error creating pet: $e');
      rethrow;
    }
  }

  @override
  Future<UpdatePetResponse> updatePet(UpdatePetRequest request) async {
    try {
      // Get token from shared preferences
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('token');

      if (token == null || token.isEmpty) {
        throw Exception('No authentication token found');
      }

      final url = Uri.parse('$baseUrl/pets/${request.id}');
      final requestBody = jsonEncode(request.toJson());

      print('🌐 Making Update Pet API request to: $url');
      print('🔑 Using token: ${token.substring(0, 20)}...');
      print('📤 Request body: $requestBody');

      final response = await client.patch(
        url,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
        body: requestBody,
      );

      print('📡 Update Pet API Response Status: ${response.statusCode}');
      print('📡 Update Pet API Response Body: ${response.body}');

      if (response.statusCode == 200 || response.statusCode == 202) {
        final jsonResponse = jsonDecode(response.body) as Map<String, dynamic>;
        final updatePetResponse = UpdatePetResponse.fromJson(jsonResponse);

        print('✅ Pet update API call successful (Status: ${response.statusCode})');
        return updatePetResponse;
      } else {
        // Handle error responses
        final errorResponse = response.body.isNotEmpty
            ? jsonDecode(response.body) as Map<String, dynamic>
            : <String, dynamic>{};

        throw Exception('Failed to update pet: ${response.statusCode} - ${errorResponse['message'] ?? response.body}');
      }
    } catch (e) {
      print('❌ Error updating pet: $e');
      rethrow;
    }
  }

  @override
  Future<List<PetResponse>> getAllPets() async {
    try {
      // Get token from shared preferences
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('token');

      if (token == null || token.isEmpty) {
        throw Exception('No authentication token found');
      }

      final url = Uri.parse('$baseUrl/Pets/owner');

      print('🌐 Making Get All Pets API request to: $url');
      print('🔑 Using token: ${token.substring(0, 20)}...');

      final response = await client.get(
        url,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      print('📡 Get All Pets API Response Status: ${response.statusCode}');
      print('📡 Get All Pets API Response Body: ${response.body}');

      if (response.statusCode == 200) {
        try {
          final jsonResponse = jsonDecode(response.body) as Map<String, dynamic>;
          print('📋 Raw JSON response: $jsonResponse');

          // Check if the response has the expected structure
          if (jsonResponse['success'] == true && jsonResponse['data'] != null) {
            final data = jsonResponse['data'] as Map<String, dynamic>;
            final petsJson = data['petsDto'] as List<dynamic>;

            print('📋 Found ${petsJson.length} pets in response');

            final pets = petsJson.map((json) {
              try {
                return PetResponse.fromJson(json as Map<String, dynamic>);
              } catch (e) {
                print('❌ Error parsing pet JSON: $e');
                print('🔍 Problematic JSON: $json');
                rethrow;
              }
            }).toList();

            print('✅ Successfully parsed ${pets.length} pets');
            return pets;
          } else {
            print('❌ API response indicates failure');
            print('🔍 Response: $jsonResponse');
            throw Exception('API response indicates failure: ${jsonResponse['message'] ?? 'Unknown error'}');
          }
        } catch (e) {
          print('❌ JSON parsing error: $e');
          print('🔍 Raw response body: ${response.body}');
          rethrow;
        }
      } else {
        print('❌ HTTP Error ${response.statusCode}');
        print('🔍 Error response body: ${response.body}');
        throw Exception('Failed to get pets: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      print('❌ Error getting pets: $e');
      rethrow;
    }
  }

  @override
  Future<bool> deletePet(String petId) async {
    try {
      // Get token from shared preferences
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('token');

      if (token == null || token.isEmpty) {
        throw Exception('No authentication token found');
      }

      final url = Uri.parse('$baseUrl/pets/$petId');

      print('🌐 Making Delete Pet API request to: $url');
      print('🔑 Using token: ${token.substring(0, 20)}...');

      final response = await client.delete(
        url,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      print('📡 Delete Pet API Response Status: ${response.statusCode}');
      print('📡 Delete Pet API Response Body: ${response.body}');

      if (response.statusCode == 200 || response.statusCode == 204) {
        print('✅ Pet deleted successfully');
        return true;
      } else {
        final errorMessage = response.body.isNotEmpty ? response.body : 'Unknown error';
        print('❌ Failed to delete pet: ${response.statusCode} - $errorMessage');
        throw Exception('Failed to delete pet: ${response.statusCode} - $errorMessage');
      }
    } catch (e) {
      print('❌ Error deleting pet: $e');
      rethrow;
    }
  }
}
