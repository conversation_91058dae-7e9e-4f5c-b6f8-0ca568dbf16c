import '../entities/pet_response.dart';
import '../repositories/pet_repository.dart';

class GetAllPetsUseCase {
  final PetRepository repository;

  GetAllPetsUseCase(this.repository);

  Future<List<PetResponse>> call() async {
    try {
      print('🐾 Fetching all pets...');
      
      final pets = await repository.getAllPets();
      
      print('✅ Successfully fetched ${pets.length} pets');
      for (final pet in pets) {
        print('  - ${pet.petName} (${pet.specie.enType}, ${pet.breed.enBreed})');
      }
      
      return pets;
    } catch (e) {
      print('❌ Error in GetAllPetsUseCase: $e');
      rethrow;
    }
  }
}
