import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'presentation/controllers/theme_controller.dart';
import 'presentation/views/home/<USER>';
import 'presentation/views/Auth/login_view.dart';
import 'core/di/dependency_injection.dart';
import 'services/pet_data_service.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize dependency injection
  await DependencyInjection.init();

  final prefs = await SharedPreferences.getInstance();
  final isLoggedIn = prefs.getBool('isLoggedIn') ?? false;

  // Initialize pet data service if user is logged in
  if (isLoggedIn) {
    print('🚀 User is logged in, initializing pet data service...');
    getIt<PetDataService>().loadAllPetData();
  }

  runApp(MyApp(isLoggedIn: isLoggedIn));
}

class MyApp extends StatefulWidget {
  const MyApp({
    super.key,
    required this.isLoggedIn,
  });

  final bool isLoggedIn;

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  final ThemeController _themeController = ThemeController();
  late ValueNotifier<bool> _isLoggedInNotifier;

  @override
  void initState() {
    super.initState();
    _isLoggedInNotifier = ValueNotifier(widget.isLoggedIn);
  }

  void _setLoggedIn(bool value) {
    _isLoggedInNotifier.value = value;
  }

  @override
  Widget build(BuildContext context) {
    return ListenableBuilder(
      listenable: _themeController,
      builder: (context, child) {
        return ValueListenableBuilder<bool>(
          valueListenable: _isLoggedInNotifier,
          builder: (context, isLoggedIn, _) {
            return MaterialApp(
              title: 'Login App',
              theme: _themeController.currentTheme,
              debugShowCheckedModeBanner: false,
              navigatorKey: GlobalKey<NavigatorState>(),
              home: isLoggedIn
                  ? HomeView(
                      themeController: _themeController,
                      onLogout: () => _setLoggedIn(false),
                    )
                  : LoginView(
                      themeController: _themeController,
                      onLogin: () => _setLoggedIn(true),
                    ),
            );
          },
        );
      },
    );
  }
}
